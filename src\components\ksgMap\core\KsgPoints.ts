import {
  TextureLoader,
  BufferGeometry,
  Vector3,
  Points,
  AdditiveBlending,
  Color,
  BufferAttribute,
  ShaderMaterial,
  PerspectiveCamera,
} from "three";

import starImg from "../assets/images/p_0.png";
import { Point } from "../types";
import { studyStatusToColor } from "../utils";
import { pointOpacityChangeAnimation } from "../animation/point";

// 纹理加载器和节点纹理
const textureLoader = new TextureLoader();
const starTexture = textureLoader.load(starImg); // 加载节点的星形纹理图片

/**
 * 节点亮度枚举 - 定义节点在不同状态下的透明度
 * 用于区分高亮和普通状态的节点
 */
enum POINT_LIGHT {
  HIGHT_LIGHT = 1, // 高亮状态：完全不透明
  LOW_LIGHT = 0.3, // 低亮度状态：半透明
}

// 导入自定义着色器代码
import vertShader from "../shader/pointVert.glsl?raw"; // 顶点着色器
import fragShader from "../shader/pointFrag.glsl?raw"; // 片段着色器

/**
 * 知识节点渲染类 - KsgPoint
 *
 * 继承自Three.js的Points类，专门用于渲染知识图谱中的节点
 * 使用自定义着色器实现高性能的大量节点渲染和特效
 *
 * 主要功能：
 * - 批量渲染大量知识节点（使用GPU加速）
 * - 支持节点的颜色、大小、透明度动态变化
 * - 实现节点的悬停高亮和聚焦效果
 * - 支持呼吸动画等特殊视觉效果
 * - 提供高效的节点查找和状态管理
 *
 * 技术特点：
 * - 使用BufferGeometry优化内存使用
 * - 自定义GLSL着色器实现复杂视觉效果
 * - 加法混合模式创建发光效果
 * - 支持实例化渲染，性能优异
 */
export default class KsgPoint extends Points {
  /** 节点数据数组 - 存储所有节点的完整信息 */
  pointsData: Point[];

  /** ID到索引的映射表 - 用于快速根据节点ID查找对应的数组索引 */
  idIndexMap: { [key: string]: number } = {};

  /** 上一个悬停节点的索引 - 用于清除之前的悬停状态 */
  lastHoverIndex: number = -1;

  /** 当前聚焦节点的索引 - 标识用户当前关注的节点 */
  focusIndex: number = -1;

  /** 呼吸动画状态标志 - 控制节点是否执行呼吸效果动画 */
  isBreathAni: boolean = false;

  /** 聚焦节点的直接子节点索引集合 - 用于高亮显示相关节点 */
  focusChildrenIndexArr: Set<number> = new Set<number>();
  /**
   * 构造函数 - 初始化知识节点渲染对象
   *
   * 创建用于渲染大量知识节点的Three.js Points对象
   * 使用BufferGeometry和自定义着色器实现高性能渲染
   *
   * @param points 节点数据数组 - 包含位置、颜色、状态等信息
   * @param total 总节点数量 - 用于预分配缓冲区大小
   * @param opacity 默认透明度 - 节点的初始透明度值 (0-1)
   * @param size 默认大小 - 节点的初始渲染大小
   */
  constructor(
    points: Point[],
    total: number,
    opacity: number = 1,
    size: number = 10
  ) {
    // 创建缓冲几何体 - 用于存储节点的顶点数据
    const pGeo = new BufferGeometry();
    // 设置一个初始点，避免几何体为空
    pGeo.setFromPoints([new Vector3(0, 0, 0)]);

    // === 创建顶点属性缓冲区 ===
    // 这些缓冲区将数据传递给GPU着色器进行并行处理

    // 位置属性 - 存储每个节点的3D坐标 (x, y, z)
    const positionAttribute = new BufferAttribute(
      new Float32Array(total * 3), // 每个节点3个浮点数 (x, y, z)
      3 // 每个顶点的分量数
    );

    // 颜色属性 - 存储每个节点的RGB颜色值
    const colorAttribute = new BufferAttribute(
      new Float32Array(total * 3), // 每个节点3个浮点数 (r, g, b)
      3 // 每个颜色的分量数
    );

    // 透明度属性 - 存储每个节点的透明度值
    const opacityAttribute = new BufferAttribute(
      new Float32Array(total), // 每个节点1个浮点数
      1 // 每个透明度的分量数
    );

    // 尺寸属性 - 存储每个节点的渲染大小
    const sizeAttribute = new BufferAttribute(
      new Float32Array(total), // 每个节点1个浮点数
      1 // 每个尺寸的分量数
    );

    // 随机数属性 - 为每个节点分配随机值，用于动画效果
    const randomAttribute = new BufferAttribute(
      new Float32Array(total), // 每个节点1个浮点数
      1 // 每个随机数的分量数
    );

    // 呼吸状态属性 - 控制节点是否执行呼吸动画
    const breathStatusAttribute = new BufferAttribute(
      new Float32Array(total), // 每个节点1个浮点数 (0或1)
      1 // 每个状态的分量数
    );

    const tempIndexMap: { [key: string]: number } = {};
    // 遍历吗每个节点
    points.forEach((point, index) => {
      // 设置位置
      positionAttribute.setXYZ(
        index,
        point.coordinate[0],
        point.coordinate[1],
        point.coordinate[2]
      );
      // 设置颜色
      const color = studyStatusToColor(point.status);
      colorAttribute.setXYZ(index, color.r, color.g, color.b);

      // 透明度
      opacityAttribute.setX(index, opacity);
      // 尺寸
      sizeAttribute.setX(index, size);
      //分配的索引
      point.index = index;
      tempIndexMap[point.id] = index;
      randomAttribute.setX(index, Math.random() * 2 * Math.PI);

      // 呼吸状态
      breathStatusAttribute.setX(index, 0);
    });
    // opacityAttribute.setX(0, 1);

    // 传入顶点着色器
    pGeo.setAttribute("position", positionAttribute);
    pGeo.setAttribute("customColor", colorAttribute);
    pGeo.setAttribute("customOpacity", opacityAttribute);
    pGeo.setAttribute("size", sizeAttribute);
    pGeo.setAttribute("customRandom", randomAttribute);
    pGeo.setAttribute("breathStatus", breathStatusAttribute);
    pGeo.setDrawRange(0, points.length); //设置需要渲染的个数
    super(
      pGeo,
      new ShaderMaterial({
        uniforms: {
          map: { value: starTexture },
          alphaMap: { value: starTexture },
          offset: { value: [-0.02, -0.02] },
          uTime: { value: Math.random() * 2 * Math.PI },
          isBreathAni: { value: false },
        },
        transparent: true,
        vertexShader: vertShader,
        fragmentShader: fragShader,
        depthWrite: false,
        blending: AdditiveBlending, //加法混合
      })
    );
    // 维护数据
    this.pointsData = JSON.parse(JSON.stringify(points));
    this.idIndexMap = tempIndexMap;
  }

  /**
   * 释放内存资源
   */
  dispose() {
    if (!this.geometry && !this.material) return;
    this.geometry.dispose();
    if (Array.isArray(this.material)) this.material.forEach((m) => m.dispose());
    else this.material.dispose();
  }

  /**
   * 根据索引获取节点数据
   * @param {number} index 索引
   */
  getPointData(index: number): Point | null {
    if (index < 0 || index >= this.pointsData.length) return null;
    return this.pointsData[index];
  }

  /**
   * 根据id查询节点数据
   * @param {string} id id
   */
  getPointDataById(id: string) {
    return this.pointsData[this.idIndexMap[id]];
  }

  /**
   *更新位置
   @param {number[]} indexArr 索引数组-支持批量操作
   @param {[number, number, number]} position 位置
   */
  updatePosition(indexArr: number[], position: [number, number, number]) {
    const positionAttribute = this.geometry.getAttribute("position");
    indexArr.forEach((index) => {
      positionAttribute.setXYZ(index, position[0], position[1], position[2]);
    });
    this.geometry.attributes.position.needsUpdate = true;
  }

  /**
   * 更新颜色
   * @param {number[]} indexArr 索引数组-支持批量操作
   * @param {Color} color 颜色
   */
  updateColor(indexArr: number[], color: Color) {
    const colorAttribute = this.geometry.getAttribute("customColor");
    indexArr.forEach((index) => {
      colorAttribute.setXYZ(index, color.r, color.g, color.b);
    });
    this.geometry.attributes.customColor.needsUpdate = true;
  }

  /**
   * 更新尺寸
   * @param {number[]} indexArr 索引数组-支持批量操作
   * @param {number} size 尺寸
   */
  updateSize(indexArr: number[], size: number) {
    const sizeAttribute = this.geometry.getAttribute("size");
    indexArr.forEach((index) => {
      sizeAttribute.setX(index, size);
    });
    this.geometry.attributes.size.needsUpdate = true;
  }

  /**
   * 更新透明度
   * @param {number[]} indexArr 索引数组-支持批量操作
   * @param {number} opacity 透明度
   */
  updateOpacity(indexArr: number[], opacity: number) {
    const opacityAttribute = this.geometry.getAttribute("customOpacity");
    indexArr.forEach((index) => {
      pointOpacityChangeAnimation(
        opacityAttribute.getX(index),
        opacity,
        (opacity) => {
          opacityAttribute.setX(index, opacity);
          this.geometry.attributes.customOpacity.needsUpdate = true;
        }
      );
    });
  }

  /**
   * 节点呼吸动画
   * @param {boolean} option 开关
   * @default true 开启状态
   */
  breathAnimationSwitch(option: boolean = true) {
    this.isBreathAni = option;
    const attributeData = option ? 1 : 0;
    const bAttributes = this.geometry.getAttribute("breathStatus");
    const pAttributes = this.geometry.getAttribute("customOpacity");
    for (let i = 0; i < bAttributes.count; i++) {
      bAttributes.setX(i, attributeData);
      pAttributes.setX(i, POINT_LIGHT.LOW_LIGHT);
    }
    this.geometry.attributes.breathStatus.needsUpdate = true;
    this.geometry.attributes.customOpacity.needsUpdate = true;
    this.lastHoverIndex = this.focusIndex = -1;
  }

  /**
   * 开启或关闭某个节点的呼吸动画
   * @param index 节点索引
   * @param enable 是否开启
   * @default true 开启状态
   */
  enablePointBreath(index: number, enable: boolean = true) {
    const attributes = this.geometry.getAttribute("breathStatus");
    const attributeData = enable ? 1 : 0;
    attributes.setX(index, attributeData);
    this.geometry.attributes.breathStatus.needsUpdate = true;
  }
  /**
   * 动画更新函数
   */
  update() {
    if (this.isBreathAni) {
      (this.material as ShaderMaterial).uniforms.uTime.value += 0.02;
    }
  }

  /**
   * 触发某个索引下节点的hover状态
   * @param {number} index 索引
   */
  toggleHover(index: number = -1) {
    if (
      (index === this.focusIndex && this.focusIndex !== -1) ||
      this.focusChildrenIndexArr.has(index)
    )
      return;
    if (index != -1) {
      if (this.lastHoverIndex != -1) {
        this.updateOpacity([this.lastHoverIndex], POINT_LIGHT.LOW_LIGHT);
        if (this.isBreathAni) this.enablePointBreath(this.lastHoverIndex, true);
      }
      if (this.isBreathAni) this.enablePointBreath(index, false);
      this.updateOpacity([index], POINT_LIGHT.HIGHT_LIGHT);
      this.lastHoverIndex = index;
    } else {
      if (this.lastHoverIndex != -1) {
        if (this.isBreathAni)
          this.enablePointBreath(this.lastHoverIndex, false);
        this.updateOpacity([this.lastHoverIndex], POINT_LIGHT.LOW_LIGHT);
      }
      this.lastHoverIndex = -1;
    }
  }

  /**
   *触发某个索引聚焦状态
   *@param {number} index 索引
   */
  toggleFocus(index: number) {
    if (this.focusIndex > -1) {
      this.updateOpacity([this.focusIndex!], POINT_LIGHT.LOW_LIGHT);
    }
    if (this.focusChildrenIndexArr.size) {
      this.focusChildrenIndexArr.forEach((fIndex) => {
        if (fIndex !== index!)
          this.updateOpacity([fIndex], POINT_LIGHT.LOW_LIGHT);
      });
    }
    this.updateOpacity([index], POINT_LIGHT.HIGHT_LIGHT);
    this.focusIndex = index;
    if (this.lastHoverIndex === index) this.lastHoverIndex = -1;
  }

  /**
   * 初始化高亮的节点,下次聚焦其他
   * 节点时可以在没聚焦状态下自动变暗
   * @param {Point[]} points 节点数组（必须携带index）
   */
  setHightLightPoints(points: Point[]) {
    if (this.focusChildrenIndexArr.size) {
      points.forEach((point) => this.focusChildrenIndexArr.add(point.index!));
    } else {
      this.focusChildrenIndexArr = new Set(points.map((point) => point.index!));
    }
  }

  /**
   *触发直接前驱状态
   *@param {number[]} indexArr 索引
   */
  toggleFocusChildren(indexArr: number[]) {
    indexArr.forEach((index) => {
      this.updateOpacity([index], POINT_LIGHT.HIGHT_LIGHT);
    });
    this.focusChildrenIndexArr = new Set(indexArr);
  }

  /**
   * 获取某个索引下节点的dnc坐标
   * @param {number} index 索引
   * @param {PerspectiveCamera} camera 相机
   * @param {number} rendererW 渲染宽度
   * @param {number} rendererH 渲染高度
   */
  getWorldP(
    index: number,
    camera: PerspectiveCamera,
    rendererW: number,
    rendererH: number
  ): { x: number; y: number } {
    const localP = new Vector3(...this.pointsData[index].coordinate);
    const wordP = new Vector3();
    this.localToWorld(wordP.copy(localP));
    wordP.project(camera); //获取二维平面dnc 坐标
    return {
      x: (wordP.x * 0.5 + 0.5) * rendererW, //变为0-1
      y: (0.5 - wordP.y * 0.5) * rendererH, //变为0-1
    };
  }

  /**
   *加载更多,更新节点
   */
  loadMore(newPointsData: Point[], opacity: number = 1, size: number = 10) {
    const positionAtr = this.geometry.getAttribute("position");
    const colorAtr = this.geometry.getAttribute("customColor");
    const opacityAtr = this.geometry.getAttribute("customOpacity");
    const sizeAtr = this.geometry.getAttribute("size");
    const randomAtr = this.geometry.getAttribute("customRandom");
    const breathAtr = this.geometry.getAttribute("breathStatus");
    newPointsData.forEach((point, index) => {
      const endIndex = index + this.pointsData.length;
      this.idIndexMap[point.id] = endIndex;
      // 设置位置
      positionAtr.setXYZ(
        endIndex,
        point.coordinate[0],
        point.coordinate[1],
        point.coordinate[2]
      );
      // 设置颜色
      const color = studyStatusToColor(point.status);
      colorAtr.setXYZ(endIndex, color.r, color.g, color.b);
      // 透明度
      opacityAtr.setX(endIndex, opacity);
      // 尺寸
      sizeAtr.setX(endIndex, size);
      //分配索引
      point.index = endIndex;
      randomAtr.setX(endIndex, Math.random() * 2 * Math.PI);
      // 呼吸状态
      breathAtr.setX(endIndex, 0);
    });
    this.geometry.attributes.position.needsUpdate = true;
    this.geometry.attributes.customColor.needsUpdate = true;
    this.geometry.attributes.size.needsUpdate = true;
    this.geometry.attributes.customOpacity.needsUpdate = true;
    this.geometry.attributes.breathStatus.needsUpdate = true;
    this.geometry.attributes.customRandom.needsUpdate = true;
    this.pointsData.push(...newPointsData);
    this.geometry.setDrawRange(0, this.pointsData.length);
    // 更新包围盒防止无法正常hover
    this.geometry.computeBoundingBox();
    this.geometry.computeBoundingSphere();

    // // 更新节点信息
    // diffData.updatePointsInfo.forEach((point) => {
    //   this.pointsData[point.index!].childIds = point.childIds;
    // });
  }

  /**
   * 更新节点信息
   * @param {Point} point 点信息
   */
  updatePointInfo(point: Point) {
    const { index, coordinate } = point;
    this.pointsData[index!].coordinate = coordinate;
    this.pointsData[index!].childIds = point.childIds;
    this.geometry.computeBoundingBox();
    this.geometry.computeBoundingSphere();
  }

  /**
   *测试方法-获取渲染的节点真实的3d坐标
   */
  getPoint3DPosition(id: string) {
    const positionAttr = this.geometry.getAttribute("position");
    const index = this.idIndexMap[id];
    console.log("index", index);
    return [
      positionAttr.getX(index),
      positionAttr.getY(index),
      positionAttr.getZ(index),
    ];
  }
}
