## KsgMap 旋转功能实现分析
### 图谱
```mermaid
graph LR
    A[KsgControls 旋转功能] --> B[手动旋转]
    A --> C[自动旋转]
    
    B --> D[鼠标旋转]
    B --> E[触摸旋转]
    B --> F[键盘旋转]
    
    D --> D1[左键拖拽]
    D --> D2[handleMouseMoveRotate]
    D --> D3[rotateLeft/rotateUp]
    
    E --> E1[单指触摸]
    E --> E2[handleTouchMoveRotate]
    E --> E3[计算旋转增量]
    
    F --> F1[Ctrl+方向键]
    F --> F2[handleKeyDown]
    F --> F3[rotateLeft/rotateUp]
    
    C --> G[autoRotateUpdate]
    C --> H[Y轴旋转矩阵]
    C --> I[相机位置更新]
    
    D3 --> J[sphericalDelta更新]
    E3 --> J
    F3 --> J
    
    J --> K[update函数]
    K --> L[球面坐标转换]
    L --> M[应用旋转变化]
    M --> N[相机位置更新]
    N --> O[lookAt目标点]
    
    G --> P[Matrix4.makeRotationY]
    P --> Q[offset.applyMatrix4]
    Q --> R[相机绕目标旋转]
    

```
- 旋转功能执行时序图
```mermaid
sequenceDiagram
    participant User as 用户
    participant DOM as DOM元素
    participant Controls as KsgControls
    participant Camera as 相机
    participant Render as 渲染循环
    
    Note over User,Render: 手动旋转流程
    User->>DOM: 鼠标左键按下
    DOM->>Controls: onPointerDown事件
    Controls->>Controls: handleMouseDownRotate
    Controls->>Controls: 设置rotateStart位置
    
    User->>DOM: 鼠标拖拽移动
    DOM->>Controls: onPointerMove事件
    Controls->>Controls: handleMouseMoveRotate
    Controls->>Controls: 计算rotateDelta
    Controls->>Controls: rotateLeft(angle)
    Controls->>Controls: rotateUp(angle)
    Controls->>Controls: 更新sphericalDelta
    Controls->>Controls: update()调用
    Controls->>Camera: 更新相机位置
    Controls->>Camera: lookAt(target)
    
    Note over User,Render: 自动旋转流程
    Render->>Controls: autoRotateUpdate(deltaTime)
    Controls->>Controls: 检查autoRotate标志
    Controls->>Controls: 计算旋转角度
    Controls->>Controls: 创建Y轴旋转矩阵
    Controls->>Controls: 应用矩阵变换
    Controls->>Camera: 更新相机位置
    Controls->>Camera: lookAt(target)
    
    Note over User,Render: 每帧更新
    Render->>Controls: update(deltaTime)
    Controls->>Controls: 球面坐标转换
    Controls->>Controls: 应用阻尼效果
    Controls->>Controls: 限制角度范围
    Controls->>Camera: 最终位置更新

```
### 根本原因分析

KsgMap 的旋转功能基于 **球面坐标系统** 和 **Three.js 的相机控制机制**，通过以下核心原理实现：

1. **球面坐标转换**：将相机的笛卡尔坐标转换为球面坐标（theta, phi, radius）
2. **事件驱动**：监听鼠标、触摸、键盘事件来触发旋转
3. **矩阵变换**：使用旋转矩阵实现自动旋转效果
4. **实时更新**：通过渲染循环每帧更新相机位置

### 实现方案

#### 1. 手动旋转实现

````typescript path=src/components/ksgMap/core/KsgControls.ts mode=EXCERPT
// 旋转控制的核心函数
function rotateLeft(angle: number) {
  sphericalDelta.theta -= angle;  // 水平旋转（绕Y轴）
}

function rotateUp(angle: number) {
  sphericalDelta.phi -= angle;    // 垂直旋转（极角）
}

// 鼠标旋转处理
function handleMouseMoveRotate(event: MouseEvent) {
  rotateEnd.set(event.clientX, event.clientY);
  rotateDelta
    .subVectors(rotateEnd, rotateStart)
    .multiplyScalar(scope.rotateSpeed);
  const element = scope.domElement;
  rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight);
  rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight);
  rotateStart.copy(rotateEnd);
  scope.update();
}
````

#### 2. 自动旋转实现

````typescript path=src/components/ksgMap/core/KsgControls.ts mode=EXCERPT
/**
 * 自动旋转函数,若要开启自动旋转功能,请设置autoRotate为true
 * 渲染帧中调用此方法
 */
autoRotateUpdate(deltaTime: number) {
  //cameral auto rotate around
  if (this.autoRotate) {
    const offset1 = this.object.position.clone().sub(this.target);
    const angle = this.autoRotateSpeed * deltaTime!;
    const rotationMatrix = new Matrix4().makeRotationY(angle);
    offset1.applyMatrix4(rotationMatrix);
    this.object.position.copy(this.target).add(offset1);
    this.object.lookAt(this.target);
  }
}
````

#### 3. 更新机制

````typescript path=src/components/ksgMap/core/KsgControls.ts mode=EXCERPT
// 核心更新函数（每帧调用）
return function update(deltaTime: number | null = null) {
  // 获取相机相对于目标的偏移向量
  offset.copy(position).sub(scope.target);
  // 转换到球面坐标
  offset.applyQuaternion(quat);
  spherical.setFromVector3(offset);
  
  // 应用旋转变化（支持阻尼效果）
  if (scope.enableDamping) {
    spherical.theta += sphericalDelta.theta * scope.dampingFactor;
    spherical.phi += sphericalDelta.phi * scope.dampingFactor;
  } else {
    spherical.theta += sphericalDelta.theta;
    spherical.phi += sphericalDelta.phi;
  }
  
  // 限制角度范围
  spherical.phi = Math.max(
    scope.minPolarAngle,
    Math.min(scope.maxPolarAngle, spherical.phi)
  );
  
  // 转换回笛卡尔坐标并更新相机位置
  offset.setFromSpherical(spherical);
  offset.applyQuaternion(quatInverse);
  position.copy(scope.target).add(offset);
  scope.object.lookAt(scope.target);
};
````

### 使用方法

#### 1. 基础配置

````typescript path=src/components/ksgMap/config/index.ts mode=EXCERPT
/** 控制器配置参数 */
controls: {
  /** 最小极角（弧度），限制垂直旋转下限 */
  minPolarAngle: 0.78539,
  /** 最大极角（弧度），限制垂直旋转上限 */
  maxPolarAngle: 2.35619,
  /** 鼠标按键映射配置 */
  mouseButtons: {
    /** 左键：旋转 */
    LEFT: MOUSE.ROTATE,
    /** 中键：缩放 */
    MIDDLE: MOUSE.DOLLY,
    /** 右键：平移 */
    RIGHT: MOUSE.PAN,
  },
  /** 是否启用阻尼效果 */
  enableDamping: true,
}
````

#### 2. 控制器属性设置

```typescript
// 启用/禁用旋转功能
controls.enableRotate = true;

// 设置旋转速度
controls.rotateSpeed = 1.0;

// 启用自动旋转
controls.autoRotate = true;
controls.autoRotateSpeed = 2.0;

// 设置角度限制
controls.minPolarAngle = 0.78539;  // 45度
controls.maxPolarAngle = 2.35619;  // 135度

// 启用阻尼效果
controls.enableDamping = true;
controls.dampingFactor = 0.05;
```

#### 3. 交互方式

1. **鼠标操作**：
   - 左键拖拽：旋转视角
   - Ctrl + 左键：平移（如果启用）

2. **触摸操作**：
   - 单指拖拽：旋转视角
   - 双指操作：缩放和平移

3. **键盘操作**：
   - Ctrl + 方向键：旋转视角
   - 方向键：平移视角

#### 4. 事件监听

````typescript path=src/components/ksgMap/config/event.ts mode=EXCERPT
// 自动旋转控制事件
function onMove() {
  if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW && ctx.controls!.autoRotate) {
    ctx.controls!.autoRotate = false;  // 用户操作时停止自动旋转
  }
}

function onMoveEnd() {
  if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW && !ctx.controls!.autoRotate) {
    ctx.controls!.autoRotate = true;   // 用户停止操作后恢复自动旋转
  }
}
````

#### 5. 渲染循环集成

````typescript path=src/components/ksgMap/hooks/useRendererFrame.ts mode=EXCERPT
function startRenderFrame(time: any = 0) {
  const deltaTime = clock.getDelta();
  
  // 相机控制器更新 - 处理用户交互（旋转、缩放、平移）
  ctx.controls?.update(deltaTime);
  // 自动旋转更新（如果启用）
  ctx.controls?.autoRotateUpdate(deltaTime);
  
  // 继续下一帧
  requestAnimationFrame(startRenderFrame);
}
````

### 关键特性

1. **双重旋转机制**：支持手动旋转和自动旋转
2. **智能切换**：用户操作时自动停止自动旋转，停止操作后恢复
3. **角度限制**：通过 minPolarAngle 和 maxPolarAngle 限制垂直旋转范围
4. **阻尼效果**：提供平滑的旋转体验
5. **多输入支持**：鼠标、触摸、键盘多种输入方式
6. **性能优化**：使用球面坐标系统和矩阵变换提高计算效率

这个旋转系统为知识图谱提供了流畅、直观的3D导航体验，用户可以通过多种方式自由探索知识空间。
# 🔄 知识图谱自动旋转功能说明
## 流程图
```mermaid
graph TD
    A[组件初始化] --> B[启动自动旋转]
    B --> C[监听用户交互事件]
    
    C --> D{用户是否有交互?}
    D -->|是| E[停止自动旋转]
    D -->|否| F[继续自动旋转]
    
    E --> G[启动5秒计时器]
    G --> H{5秒内有新交互?}
    H -->|是| I[重置计时器]
    H -->|否| J[重新启动自动旋转]
    
    I --> G
    J --> F
    F --> D
    
    K[鼠标事件] --> D
    L[触摸事件] --> D
    M[键盘事件] --> D
    N[滚轮事件] --> D
    
```
- 自动旋转功能时序图
```mermaid

sequenceDiagram
    participant User as 用户
    participant KsgControls as KsgControls
    participant AutoRotateManager as AutoRotateManager
    participant Timer as 定时器系统
    
    Note over KsgControls: 组件初始化
    KsgControls->>AutoRotateManager: 创建管理器实例
    AutoRotateManager->>KsgControls: 设置 autoRotate = true
    AutoRotateManager->>KsgControls: 监听 'start' 事件
    Note over AutoRotateManager: 自动旋转开始运行
    
    User->>KsgControls: 鼠标拖拽/触摸/键盘操作
    KsgControls->>AutoRotateManager: 触发 'start' 事件
    AutoRotateManager->>KsgControls: 设置 autoRotate = false
    AutoRotateManager->>Timer: 启动5秒倒计时
    Note over AutoRotateManager: 自动旋转暂停
    
    alt 5秒内有新交互
        User->>KsgControls: 继续操作
        KsgControls->>AutoRotateManager: 再次触发 'start' 事件
        AutoRotateManager->>Timer: 清除现有计时器
        AutoRotateManager->>Timer: 重新启动5秒倒计时
        Note over AutoRotateManager: 计时器重置
    else 5秒内无新交互
        Timer->>AutoRotateManager: 倒计时结束
        AutoRotateManager->>KsgControls: 设置 autoRotate = true
        Note over AutoRotateManager: 自动旋转恢复
    end
    
    Note over KsgControls: 组件销毁
    KsgControls->>AutoRotateManager: 调用 destroy()
    AutoRotateManager->>Timer: 清除所有计时器
    AutoRotateManager->>KsgControls: 移除事件监听器
    Note over AutoRotateManager: 资源清理完成
```
## 概述

本文档详细说明了知识图谱组件的自动旋转功能实现，包括初始化时自动启动和用户无操作5秒后重新启动的完整机制。

## 功能特性

### ✨ 核心功能
- **初始化自动启动**：组件加载完成后自动开始旋转
- **智能交互检测**：检测鼠标、触摸、键盘等所有用户交互
- **延迟重启机制**：用户停止操作5秒后自动重新启动旋转
- **防抖处理**：连续交互时重置计时器，避免频繁启停
- **完整生命周期管理**：组件销毁时自动清理资源

### 🎯 设计目标
- 提升用户体验：无需手动操作即可欣赏3D场景
- 智能响应：用户操作时立即停止，避免干扰
- 节能考虑：长时间无操作时恢复自动展示
- 稳定可靠：完善的错误处理和资源管理

## 技术实现

### 架构设计

```mermaid
graph TD
    A[KsgControls] --> B[AutoRotateManager]
    B --> C[事件监听器]
    B --> D[定时器管理]
    B --> E[状态管理]
    
    C --> F[start事件监听]
    D --> G[延迟重启定时器]
    E --> H[旋转状态控制]
    
    F --> I[用户交互检测]
    G --> J[5秒延迟重启]
    H --> K[autoRotate属性]
```

### 核心组件

#### 1. AutoRotateManager 类
负责自动旋转的完整生命周期管理：

```typescript
class AutoRotateManager {
  constructor(controls: KsgControls, options?: AutoRotateManagerOptions)
  start(): void              // 启动自动旋转
  stop(): void               // 停止自动旋转
  pause(): void              // 暂停并启动重启计时器
  resetTimer(): void         // 重置重启计时器
  destroy(): void            // 销毁管理器
  get isActive(): boolean    // 获取当前状态
  setSpeed(speed: number): void // 设置旋转速度
}
```

#### 2. 配置选项
```typescript
interface AutoRotateManagerOptions {
  restartDelay?: number;     // 重启延迟时间（默认5000ms）
  autoStart?: boolean;       // 是否自动启动（默认true）
  rotateSpeed?: number;      // 旋转速度（默认2.0）
  debug?: boolean;           // 调试模式（默认false）
}
```

### 工作流程

#### 初始化流程
1. **KsgControls构造函数**执行
2. **延迟100ms**后创建AutoRotateManager实例
3. **自动启动**自动旋转功能
4. **开始监听**用户交互事件

#### 交互处理流程
1. **用户开始交互**（鼠标按下、触摸开始等）
2. **触发start事件**
3. **AutoRotateManager检测**到事件
4. **立即停止**自动旋转
5. **启动5秒倒计时**
6. **倒计时结束**后重新启动自动旋转

#### 连续交互处理
1. **检测到新的交互**
2. **清除现有计时器**
3. **重新启动5秒倒计时**
4. **确保不会过早重启**

## 使用方法

### 基本使用
自动旋转功能已集成到KsgControls中，无需额外配置：

```typescript
// 创建控制器时自动启用自动旋转
const controls = new KsgControls(camera, rootArea, domElement);
// 自动旋转会在初始化后自动启动
```

### 自定义配置
如果需要自定义配置，可以在控制器创建后访问管理器：

```typescript
// 等待管理器初始化完成
setTimeout(() => {
  if (controls.autoRotateManager) {
    // 设置不同的旋转速度
    controls.autoRotateManager.setSpeed(1.0);
    
    // 手动控制
    controls.autoRotateManager.stop();
    controls.autoRotateManager.start();
  }
}, 200);
```

### 高级用法
```typescript
// 监听自动旋转状态变化
function checkAutoRotateStatus() {
  const isActive = controls.autoRotateManager?.isActive;
  console.log('自动旋转状态:', isActive ? '运行中' : '已停止');
}

// 定期检查状态
setInterval(checkAutoRotateStatus, 1000);
```

