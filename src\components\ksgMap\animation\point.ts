import { Tween, Easing } from "@tweenjs/tween.js";
import { Mesh, ShaderMaterial, Points } from "three";
import KsgPoint from "../core/KsgPoints";
/**
 * 聚焦进入动画
 * @param {Mesh} haloMesh 聚焦点光圈
 * @param {number} duration 持续时间
 */
export function pointHaloEnterAnimation(
  haloMesh: Mesh,
  duration: number = 500
) {
  return new Promise((resolve) => {
    new Tween({ opacity: 0, scale: 2.2 })
      .to({ opacity: 1, scale: 1 }, duration)
      .onUpdate(({ opacity, scale }) => {
        (haloMesh.material as ShaderMaterial).uniforms.opacity.value = opacity;
        haloMesh.scale.set(scale, scale, scale);
      })
      .start()
      .onComplete(resolve);
  });
}
/**
 * 消失动画
 * @param {Mesh} haloMesh 聚焦点光圈
 * @param {number} duration 持续时间
 */
export function pointHaloLeaveAnimation(
  haloMesh: Mesh,
  duration: number = 500
) {
  return new Promise((resolve) => {
    new Tween({ scale: 1 })
      .to({ scale: 0.1 }, duration)
      .onUpdate(({ scale }) => {
        haloMesh.scale.set(scale, scale, scale);
      })
      .start()
      .onComplete(resolve);
  });
}

/**
 * 透明度变化动画
 * @param {number} from 起始透明度
 * @param {number} to 最终透明度
 * @param {number} duration 持续时间
 */
export function pointOpacityChangeAnimation(
  from: number,
  to: number,
  callback: (opacity: number) => void,
  duration: number = 200
) {
  if (to === from) Promise.resolve();
  return new Promise((resolve) => {
    new Tween({
      opacity: from,
    })
      .to({ opacity: to }, duration)
      .onUpdate(({ opacity }) => {
        callback(opacity);
      })
      .start()
      .onComplete(resolve);
  });
}
