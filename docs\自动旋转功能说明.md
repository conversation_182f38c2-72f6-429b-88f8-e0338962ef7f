# 🔄 知识图谱自动旋转功能说明

## 概述

本文档详细说明了知识图谱组件的自动旋转功能实现，包括初始化时自动启动和用户无操作5秒后重新启动的完整机制。

## 功能特性

### ✨ 核心功能
- **初始化自动启动**：组件加载完成后自动开始旋转
- **智能交互检测**：检测鼠标、触摸、键盘等所有用户交互
- **延迟重启机制**：用户停止操作5秒后自动重新启动旋转
- **防抖处理**：连续交互时重置计时器，避免频繁启停
- **完整生命周期管理**：组件销毁时自动清理资源

### 🎯 设计目标
- 提升用户体验：无需手动操作即可欣赏3D场景
- 智能响应：用户操作时立即停止，避免干扰
- 节能考虑：长时间无操作时恢复自动展示
- 稳定可靠：完善的错误处理和资源管理

## 技术实现

### 架构设计

```mermaid
graph TD
    A[KsgControls] --> B[AutoRotateManager]
    B --> C[事件监听器]
    B --> D[定时器管理]
    B --> E[状态管理]
    
    C --> F[start事件监听]
    D --> G[延迟重启定时器]
    E --> H[旋转状态控制]
    
    F --> I[用户交互检测]
    G --> J[5秒延迟重启]
    H --> K[autoRotate属性]
```

### 核心组件

#### 1. AutoRotateManager 类
负责自动旋转的完整生命周期管理：

```typescript
class AutoRotateManager {
  constructor(controls: KsgControls, options?: AutoRotateManagerOptions)
  start(): void              // 启动自动旋转
  stop(): void               // 停止自动旋转
  pause(): void              // 暂停并启动重启计时器
  resetTimer(): void         // 重置重启计时器
  destroy(): void            // 销毁管理器
  get isActive(): boolean    // 获取当前状态
  setSpeed(speed: number): void // 设置旋转速度
}
```

#### 2. 配置选项
```typescript
interface AutoRotateManagerOptions {
  restartDelay?: number;     // 重启延迟时间（默认5000ms）
  autoStart?: boolean;       // 是否自动启动（默认true）
  rotateSpeed?: number;      // 旋转速度（默认2.0）
  debug?: boolean;           // 调试模式（默认false）
}
```

### 工作流程

#### 初始化流程
1. **KsgControls构造函数**执行
2. **延迟100ms**后创建AutoRotateManager实例
3. **自动启动**自动旋转功能
4. **开始监听**用户交互事件

#### 交互处理流程
1. **用户开始交互**（鼠标按下、触摸开始等）
2. **触发start事件**
3. **AutoRotateManager检测**到事件
4. **立即停止**自动旋转
5. **启动5秒倒计时**
6. **倒计时结束**后重新启动自动旋转

#### 连续交互处理
1. **检测到新的交互**
2. **清除现有计时器**
3. **重新启动5秒倒计时**
4. **确保不会过早重启**

## 使用方法

### 基本使用
自动旋转功能已集成到KsgControls中，无需额外配置：

```typescript
// 创建控制器时自动启用自动旋转
const controls = new KsgControls(camera, rootArea, domElement);
// 自动旋转会在初始化后自动启动
```

### 自定义配置
如果需要自定义配置，可以在控制器创建后访问管理器：

```typescript
// 等待管理器初始化完成
setTimeout(() => {
  if (controls.autoRotateManager) {
    // 设置不同的旋转速度
    controls.autoRotateManager.setSpeed(1.0);
    
    // 手动控制
    controls.autoRotateManager.stop();
    controls.autoRotateManager.start();
  }
}, 200);
```

### 高级用法
```typescript
// 监听自动旋转状态变化
function checkAutoRotateStatus() {
  const isActive = controls.autoRotateManager?.isActive;
  console.log('自动旋转状态:', isActive ? '运行中' : '已停止');
}

// 定期检查状态
setInterval(checkAutoRotateStatus, 1000);
```

## 测试验证

### 功能测试清单
- [ ] **初始化测试**：页面加载后自动旋转是否启动
- [ ] **鼠标交互测试**：拖拽时是否立即停止旋转
- [ ] **触摸交互测试**：触摸操作时是否正确响应
- [ ] **键盘交互测试**：键盘操作时是否停止旋转
- [ ] **滚轮交互测试**：滚轮缩放时是否停止旋转
- [ ] **延迟重启测试**：停止操作5秒后是否重新启动
- [ ] **连续交互测试**：持续操作时计时器是否正确重置
- [ ] **资源清理测试**：组件销毁时是否正确清理

### 测试方法
1. **打开测试页面**：`test-auto-rotate.html`
2. **观察初始状态**：确认自动旋转已启动
3. **进行各种交互**：测试鼠标、触摸、键盘操作
4. **验证延迟重启**：停止操作后等待5秒
5. **检查状态显示**：观察状态监控面板

## 故障排除

### 常见问题

#### 1. 自动旋转未启动
**可能原因**：
- 管理器初始化失败
- 控制器未正确创建
- 配置参数错误

**解决方法**：
```typescript
// 检查管理器是否存在
if (!controls.autoRotateManager) {
  console.error('AutoRotateManager未初始化');
}

// 手动启动
controls.autoRotateManager?.start();
```

#### 2. 交互后未停止旋转
**可能原因**：
- 事件监听器未正确绑定
- start事件未正确触发

**解决方法**：
```typescript
// 检查事件监听器
console.log('控制器事件监听器:', controls.listeners);

// 手动暂停
controls.autoRotateManager?.pause();
```

#### 3. 延迟重启不工作
**可能原因**：
- 计时器被意外清除
- 管理器状态异常

**解决方法**：
```typescript
// 重置计时器
controls.autoRotateManager?.resetTimer();

// 检查管理器状态
console.log('管理器状态:', controls.autoRotateManager?.isActive);
```

### 调试模式
启用调试模式可以查看详细的运行日志：

```typescript
// 在管理器初始化时启用调试
const manager = new AutoRotateManager(controls, {
  debug: true  // 启用调试日志
});
```

## 性能考虑

### 优化措施
- **延迟初始化**：避免阻塞主要初始化流程
- **事件防抖**：避免频繁的启停操作
- **资源清理**：确保定时器和事件监听器正确清理
- **状态缓存**：减少重复的状态检查

### 内存管理
- 组件销毁时自动清理所有资源
- 定时器使用完毕后立即清除
- 事件监听器在不需要时及时移除

## 更新日志

### v1.0.0 (2024-08-07)
- ✨ 新增自动旋转管理器
- ✨ 实现初始化自动启动功能
- ✨ 实现5秒延迟重启机制
- ✨ 添加完整的生命周期管理
- 📝 完善文档和测试用例
