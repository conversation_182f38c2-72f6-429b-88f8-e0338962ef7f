uniform float uOpacity;
uniform float uTime;
uniform float uWidth; //高亮长度 0.0 - 1.0
uniform float uSpeed; //流光速度 0.0 - 1.0
uniform float uOpacityOffset;  //流光透明度偏移,在原有的基础上的增量 0.0 - 1.0
uniform float uProgress;
uniform bool uIsRandom;

varying float vIndex;
varying vec3 vPosition;
varying vec3 vColor;
varying float vSegmentProgress;
varying float vCustomRandom;

float getY(float x){
  if(x>=2.0)return 2.0;
  if(x<=-1.5)return -1.5;
  return x;
}

float progressUnderRandom(float randomStart,float ux){
   float x = randomStart + ux;
  // return mod(x,3.5) - 2.5;
  return (2.0 + 1.5) * (mod(x, 3.5)/3.5) - 1.5;
  // return ux;
}

void main(){
  // 每条线段根据 index 有不同偏移
  // float offset = mod(vIndex, 2.0);
  // float progress = fract(uTime * uSpeed); // 0~1 动画进度
  //  float progress = uProgress;
   float progress = uIsRandom ? progressUnderRandom(vCustomRandom,uProgress) : uProgress;

  // 高亮段从 progress - width/2 到 progress + width/2
  float halfWidth = uWidth / 2.0;
  float alpha = smoothstep(progress - halfWidth, progress, vSegmentProgress)
              * (1.0 - smoothstep(progress, progress + halfWidth, vSegmentProgress));

  float finalOpacity = mix(uOpacity, uOpacity + uOpacityOffset, alpha); // 高亮段不透明，其余部分较低透明度

  gl_FragColor = vec4(vColor, finalOpacity);
}

