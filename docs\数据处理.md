


## 知识图谱库数据处理机制分析

### 根本原因分析

这个知识图谱库采用了**分层异步处理架构**，主要解决以下核心问题：

1. **大规模数据渲染性能问题** - 通过GPU加速和异步计算避免UI阻塞
2. **复杂图结构布局问题** - 使用DAG算法实现层次化3D布局
3. **动态数据更新问题** - 通过增量更新和差异计算实现流畅的数据变更

### 数据处理核心流程

#### 1. 数据输入与预处理阶段

````typescript path=src/components/ksgMap/types/index.ts mode=EXCERPT
export type PointData = {
  pointId: string;
  pointName: string;
  parentPointIds: string[];
  isMilestone: boolean;
  status: number;
};
````

原始数据通过`dataLoader`进行预处理：
- **MathJax公式渲染** - 支持数学公式显示
- **标题格式化** - 文本内容标准化
- **状态初始化** - 设置学习进度等状态

#### 2. 图结构计算引擎 (KsgGraph)
- 图布局算法流程
```mermaid
flowchart TD
    A[原始节点数据] --> B[构建图结构]
    B --> C[解析父子关系]
    C --> D[建立邻接表]
    
    D --> E[计算层级]
    E --> F[拓扑排序 - BFS]
    F --> G[检测环路]
    G --> H{是否存在环?}
    H -->|是| I[抛出异常]
    H -->|否| J[层级计算完成]
    
    J --> K[计算3D坐标]
    K --> L{节点数量判断}
    L -->|单节点| M["放置在层级中心 (0, y, 0)"]
    L -->|多节点| N[同心圆分布算法]
    
    N --> O["计算圆环半径 r = round * pointSpace"]
    O --> P["计算圆周位置 x = r*cos(θ), z = r*sin(θ)"]
    P --> Q["微调Y坐标 y += sin(round)"]
    Q --> R[分配节点到圆环位置]
    
    M --> S[位置计算完成]
    R --> S
    S --> T[触发渲染]
```
这是整个数据处理的核心，分为三个关键步骤：

**步骤1: 构建图结构 (build方法)**

````typescript path=src/components/ksgMap/core/KsgGraph.ts mode=EXCERPT
private build(pointsData: PointData[]) {
  // 将父关系转换为子关系，构建双向图结构
  for (const point of pointsData) {
    this.pointsData.set(point.pointId, {
      id: point.pointId,
      name: point.pointName,
      parentIds: point.parentPointIds,
      childIds: [], // 动态构建子节点关系
      level: -1,
      coordinate: [0, 0, 0],
      // ...其他属性
    });
  }
}
````

**步骤2: 层级计算 (computeLevel方法)**

使用**拓扑排序**和**BFS算法**计算节点层级：

````typescript path=src/components/ksgMap/core/KsgGraph.ts mode=EXCERPT
computeLevel(points: Map<string, Point>) {
  // 计算入度
  let degrees: { [key: string]: number } = {};
  // BFS层次遍历
  while (queue.length) {
    const id = queue.shift()!;
    const point = scope.pointsData.get(id)!;
    for (const childId of point.childIds) {
      degrees[childId]--;
      if (degrees[childId] === 0) {
        const crtLevel = scope.pointsData.get(id)!.level + 1;
        points.get(childId)!.level = crtLevel;
        queue.push(childId);
      }
    }
  }
}
````

**步骤3: 3D坐标计算 (computePointPosition方法)**

采用**圆形分层布局算法**：

````typescript path=src/components/ksgMap/core/KsgGraph.ts mode=EXCERPT
computePointPosition(levelHeight: number = 15, pointSpace: number = 7) {
  // 每层节点按圆形分布
  for (let round = 1, s = count; s != 0; ++round) {
    const r = round * pointSpace;
    for (let i = 0; i < num; ++i) {
      const x = r * Math.cos((2 * Math.PI * i) / num);
      const z = r * Math.sin((2 * Math.PI * i) / num);
      this.pointsData.get(id)!.coordinate = [x, y, z];
    }
  }
}
````

#### 3. 异步任务调度系统 (FrameScheduler)

````typescript path=src/components/ksgMap/utils/FrameScheduler.ts mode=EXCERPT
export default class FrameScheduler {
  private taskQueue: (() => boolean)[] = [];
  
  runNextTask() {
    requestAnimationFrame(() => {
      const task = this.taskQueue.shift();
      if (task) {
        const isCompleted = task();
        if (!isCompleted) this.runNextTask();
      }
    });
  }
}
````

**关键特性：**
- 使用`requestAnimationFrame`分帧执行
- 避免长时间计算阻塞主线程
- 支持任务完成回调机制

#### 4. 增量更新机制

````typescript path=src/components/ksgMap/core/KsgGraph.ts mode=EXCERPT
export type DiffData = {
  newPoints: Point[];
  updatePoints: Map<string, ModifyPoint>;
};

loadMore(pointsData: PointData[]): Promise<DiffData> {
  // 记录变化前的状态
  pointsData.map((point) => {
    let oldPoint = this.pointsData.get(point.pointId)!;
    if (oldPoint) {
      oldPoint = clonePoint(oldPoint);
      diffData.updatePoints.set(oldPoint.id, { old: oldPoint });
    }
  });
}
````

### 渲染层数据处理

#### 1. 节点渲染 (KsgPoint)

````typescript path=src/components/ksgMap/core/KsgPoints.ts mode=EXCERPT
export default class KsgPoint extends Points {
  pointsData: Point[];
  idIndexMap: { [key: string]: number } = {};
  
  updatePointsData(points: Point[]) {
    // 维护ID到索引的映射
    this.pointsData = JSON.parse(JSON.stringify(points));
    this.idIndexMap = tempIndexMap;
  }
}
````

**技术特点：**
- 继承Three.js的Points类
- 使用GPU着色器批量渲染
- 支持动态颜色、大小变化
- 实现高效的节点查找机制

#### 2. 数据状态管理

通过全局上下文`ctx`管理所有数据状态：

````typescript path=src/components/ksgMap/ctx/index.ts mode=EXCERPT
export type Context = {
  model: MODE;
  scene: Scene;
  graph: KsgGraph;
  focusPointInfo: { pointId: string };
  pointsLevelPager: {
    current: number;
    levelSize: number;
    total: number;
  };
  // ...其他状态
};
````

