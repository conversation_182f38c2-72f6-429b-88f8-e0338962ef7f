/**
 * 旋转标签管理器 - RotateLabelManager.ts
 * 
 * 职责：
 * 1. 在自动旋转时动态显示最靠近用户的节点标签
 * 2. 实时计算节点到相机的距离
 * 3. 管理旋转标签的显示和隐藏
 * 4. 与现有标签系统协调工作
 * 
 * 特性：
 * - 高性能的距离计算
 * - 防抖机制避免频繁切换
 * - 可视范围检测
 * - 完整的生命周期管理
 */

import { Vector3 } from 'three';
import type { Point } from '../types';
import { KsgLabel } from './KsgLabel';
import ctx from '../ctx';

/**
 * 旋转标签管理器配置选项
 */
export interface RotateLabelManagerOptions {
  /** 标签切换的防抖延迟时间（毫秒），默认200ms */
  debounceDelay?: number;
  /** 最大检测距离，超过此距离的节点不会显示标签，默认100 */
  maxDistance?: number;
  /** 是否启用调试日志，默认false */
  debug?: boolean;
  /** 标签偏移量，默认{x: 15, y: 15} */
  labelOffset?: { x: number; y: number };
}

/**
 * 旋转标签管理器类
 * 负责在自动旋转时管理最近节点的标签显示
 */
export class RotateLabelManager {
  /** 配置选项 */
  private options: Required<RotateLabelManagerOptions>;
  /** 旋转专用标签实例 */
  private rotateLabel: KsgLabel;
  /** 当前显示的节点 */
  private currentPoint: Point | null = null;
  /** 防抖定时器 */
  private debounceTimer: number | NodeJS.Timeout | null = null;
  /** 是否已激活 */
  private active = false;
  /** 是否已销毁 */
  private destroyed = false;
  /** 临时向量，用于距离计算 */
  private tempVector = new Vector3();

  /**
   * 构造函数
   * @param options 配置选项
   */
  constructor(options: RotateLabelManagerOptions = {}) {
    this.options = {
      debounceDelay: options.debounceDelay ?? 200,
      maxDistance: options.maxDistance ?? 100,
      debug: options.debug ?? false,
      labelOffset: options.labelOffset ?? { x: 15, y: 15 }
    };

    // 创建专用的旋转标签实例
    this.rotateLabel = new KsgLabel(this.options.labelOffset);
    
    // 添加到场景中
    ctx.viewGroup?.add(this.rotateLabel);

    this.log('RotateLabelManager initialized', this.options);
  }

  /**
   * 激活旋转标签管理器
   * 开始监控最近节点并显示标签
   */
  activate(): void {
    if (this.destroyed) {
      this.log('Cannot activate: manager is destroyed');
      return;
    }

    this.active = true;
    this.log('RotateLabelManager activated');
  }

  /**
   * 停用旋转标签管理器
   * 隐藏当前标签并停止监控
   */
  deactivate(): void {
    this.active = false;
    this.hideCurrentLabel();
    this.clearDebounceTimer();
    this.log('RotateLabelManager deactivated');
  }

  /**
   * 更新函数 - 在渲染循环中调用
   * 计算最近节点并更新标签显示
   */
  update(): void {
    if (!this.active || this.destroyed || !ctx.pointsMesh || !ctx.camera) {
      return;
    }

    // 查找最近的可见节点
    const closestPoint = this.findClosestVisiblePoint();
    
    // 如果最近节点发生变化，更新标签显示
    if (closestPoint !== this.currentPoint) {
      this.scheduleUpdateLabel(closestPoint);
    }
  }

  /**
   * 销毁管理器
   * 清理所有资源
   */
  destroy(): void {
    this.deactivate();
    
    // 从场景中移除标签
    if (this.rotateLabel.parent) {
      this.rotateLabel.parent.remove(this.rotateLabel);
    }
    
    this.destroyed = true;
    this.log('RotateLabelManager destroyed');
  }

  /**
   * 获取当前激活状态
   */
  get isActive(): boolean {
    return this.active;
  }

  /**
   * 查找最近的可见节点
   * @private
   */
  private findClosestVisiblePoint(): Point | null {
    if (!ctx.pointsMesh || !ctx.camera) return null;

    let closestPoint: Point | null = null;
    let minDistance = this.options.maxDistance;

    // 遍历所有节点，找到最近的可见节点
    ctx.pointsMesh.pointsData.forEach((point) => {
      // 跳过聚焦节点和子节点（它们有自己的标签显示逻辑）
      if (point.index === ctx.pointsMesh?.focusIndex || 
          ctx.focusChildrenIndex?.has(point.index!)) {
        return;
      }

      // 计算节点到相机的距离
      this.tempVector.set(...point.coordinate);
      const distance = this.tempVector.distanceTo(ctx.camera!.position);

      // 检查是否在可视范围内且距离更近
      if (distance < minDistance && this.isPointVisible(point)) {
        minDistance = distance;
        closestPoint = point;
      }
    });

    return closestPoint;
  }

  /**
   * 检查节点是否在可视范围内
   * @private
   */
  private isPointVisible(point: Point): boolean {
    if (!ctx.pointsMesh || !ctx.camera || !ctx.viewRange) return false;

    try {
      // 计算视口的宽度和高度
      const viewWidth = ctx.viewRange.maxX - ctx.viewRange.minX;
      const viewHeight = ctx.viewRange.maxY - ctx.viewRange.minY;

      // 获取节点的屏幕坐标
      const screenPos = ctx.pointsMesh.getWorldP(
        point.index!,
        ctx.camera,
        viewWidth,
        viewHeight
      );

      // 检查是否在视口范围内
      return screenPos.x >= ctx.viewRange.minX &&
             screenPos.x <= ctx.viewRange.maxX &&
             screenPos.y >= ctx.viewRange.minY &&
             screenPos.y <= ctx.viewRange.maxY;
    } catch (error) {
      this.log('Error checking point visibility:', error);
      return false;
    }
  }

  /**
   * 安排更新标签显示（带防抖）
   * @private
   */
  private scheduleUpdateLabel(newPoint: Point | null): void {
    this.clearDebounceTimer();
    
    this.debounceTimer = setTimeout(() => {
      this.updateLabel(newPoint);
      this.debounceTimer = null;
    }, this.options.debounceDelay);
  }

  /**
   * 更新标签显示
   * @private
   */
  private updateLabel(newPoint: Point | null): void {
    if (this.destroyed || !this.active) return;

    // 隐藏当前标签
    this.hideCurrentLabel();

    // 显示新标签
    if (newPoint) {
      this.showLabel(newPoint);
    }

    this.currentPoint = newPoint;
    this.log('Label updated to point:', newPoint?.name || 'none');
  }

  /**
   * 显示指定节点的标签
   * @private
   */
  private showLabel(point: Point): void {
    if (!ctx.pointsMesh || !ctx.camera || !ctx.viewRange) return;

    try {
      // 获取节点的屏幕坐标
      const screenPos = ctx.pointsMesh.getWorldP(
        point.index!,
        ctx.camera,
        ctx.viewRange.width * window.devicePixelRatio,
        ctx.viewRange.height * window.devicePixelRatio
      );

      // 显示标签
      this.rotateLabel.display(point, {
        viewRange: ctx.viewRange,
        dnc: screenPos
      });

      this.log('Showing label for point:', point.name);
    } catch (error) {
      this.log('Error showing label:', error);
    }
  }

  /**
   * 隐藏当前标签
   * @private
   */
  private hideCurrentLabel(): void {
    if (this.rotateLabel.visible) {
      this.rotateLabel.hide();
    }
  }

  /**
   * 清除防抖定时器
   * @private
   */
  private clearDebounceTimer(): void {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer as number);
      this.debounceTimer = null;
    }
  }

  /**
   * 调试日志输出
   * @private
   */
  private log(...args: any[]): void {
    if (this.options.debug) {
      console.log('[RotateLabelManager]', ...args);
    }
  }
}
