<script setup lang="ts">
// import { MODE, type Options } from "@endlessorigin/KsgMap";
import { KsgMap, MODE, type Options } from "./components/ksgMap";
import { ref, onMounted } from "vue";
import Stats from "three/examples/jsm/libs/stats.module.js";
import testAPI, {
  multiplyRootsAPI,
  localModeApi,
  localMode2Api,
} from "./network/api";

const request = localMode2Api;
const requestChampion = multiplyRootsAPI;
//模拟一个领域的数据
const container = ref<HTMLElement>();
const stats = new Stats();
const ksgRef = ref<any>();
let loading = ref<"loading" | "loaded" | "error">("loading");
// 场景配置
// const config: Options = {
//   model: MODE.MULTIPLE_ROOT, //多根节点模式
//   // 配置分层加载
//   pointsLevelPager: {
//     current: 1, //当前层
//     levelSize: 1, //获取多少层
//   },
// };
const config: Options = {
  model: MODE.Single_ROOT, //单根节点模式
  // 配置分层加载
  pointsLevelPager: {
    current: 1, //当前层
    levelSize: 1, //获取多少层
  },
};
const dataList = [
  {
    pointId: "K1907350322875195392",
    pointName: "Java",
    status: 0,
    isMilestone: null,
    parentPointIds: ["K1907350033191395328"],
  },
  {
    pointId: "K1907350322875195391",
    pointName: "JavaSE",
    status: 0,
    isMilestone: null,
    parentPointIds: ["K1907350033191395328", "K1907350322875195392"],
  },
  {
    pointId: "K1907349828001849340",
    pointName: "ABC",
    status: 0,
    isMilestone: null,
    parentPointIds: ["K1907350033191395328"],
  },
  {
    pointId: "K1907349828001849350",
    pointName: "ksgmap",
    status: 1,
    isMilestone: null,
    parentPointIds: ["K1907350033191395328"],
  },
   {
    pointId: "K19073498228001849350",
    pointName: "ADD",
    status: 1,
    isMilestone: null,
    parentPointIds: ["K1907350033191395328"],
  },
   {
    pointId: "K19073498280201849350",
    pointName: "ABC",
    status: 1,
    isMilestone: null,
    parentPointIds: ["K1907350033191395328"],
  },
  {
    pointId: "K1907350033191395328",
    pointName: "<h1>知识图谱</h1>",
    status: 0,
    isMilestone: null,
    parentPointIds: ["K1907349828001849341"],
  },
];
const total = dataList.length;
async function init() {
  stats.showPanel(0);
  stats.dom.style.position = "absolute";
  stats.dom.style.transform = "scale(2)";
  stats.dom.style.zIndex = "10";
  stats.dom.style.top = "30px";
  stats.dom.style.left = "40px";
  container.value?.appendChild(stats.dom);
  update();

  loading.value = "loading";
  // const {
  //   // @ts-ignore
  //   data: { records, total },
  //   /**
  //    * 这里测试只获取一层单个根节点
  //    */
  // } = await requestChampion(0, 10, "P1935945912330551296", 812);

  // ksgRef.value?.firstLoadPointsData(records, total);
  // ksgRef.value?.firstLoadPointsData(dataList, total);
  ksgRef.value?.firstLoadPointsData(dataList, total, "K1907350033191395328");

  loading.value = "loaded";
}

function update() {
  requestAnimationFrame(update);
  stats.update();
}
onMounted(init);
async function handleLoadMore(rootId: string, crt: number, levelSize: number) {
  loading.value = "loading";
  const {
    // @ts-ignore
    data: { dataList, total },
  } = await request(crt, levelSize, rootId);
  ksgRef.value?.loadMorePointsData(dataList);
  loading.value = "loaded";
}
function handleClickLabel(id: string) {
  console.log("handleClickLabel 被触发，节点ID:", id);
  // window.open(`https://www.endlessorigin.com/klgdetail?klgCode=${id}`);
  alert(`点击了标签，节点ID: ${id}`);
}
</script>

<template>
  <div class="container" ref="container">
    <!-- @vue-ignore -->
    <KsgMap
      ref="ksgRef"
      :config="config"
      @load-more="handleLoadMore"
      :loading="loading"
      @click-label="handleClickLabel"
    />
  </div>
</template>

<style scoped>
.container {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
</style>
