/**
 * KsgMap 画布尺寸更新模块
 * 
 * 该模块负责处理画布容器尺寸变化时的相关更新操作，
 * 包括渲染器尺寸、相机宽高比、视口范围等的同步更新。
 * 
 */

import ctx from "../ctx";

/**
 * 画布尺寸更新函数
 * 当画布容器尺寸发生变化时，调用此函数来更新相关的渲染配置。
 * 该函数会同步更新以下内容：
 * - WebGL渲染器尺寸
 * - CSS2D渲染器尺寸
 * - 相机宽高比和投影矩阵
 * - 视口范围坐标
 * 
 * @param wrapperEle - 画布容器的DOM元素
 * @returns 返回更新后的宽度和高度，如果必要的渲染器未初始化则返回undefined
 * 
 */
export default function useUpdate(wrapperEle: HTMLElement) {
  // 安全检查：确保必要的渲染器和相机已初始化
  if (!ctx.renderer || !ctx.css2dRenderer || !ctx.camera) return;
  
  // 获取容器的实际显示尺寸（考虑CSS变换）
  const { width, height } = wrapperEle.getBoundingClientRect();
  
  // 更新WebGL渲染器的画布尺寸
  ctx.renderer?.setSize(width, height);
  
  // 更新CSS2D渲染器的画布尺寸，保持与WebGL渲染器同步
  ctx.css2dRenderer?.setSize(width, height);
  
  // 更新相机的宽高比，避免图像拉伸变形
  ctx.camera!.aspect = width / height;
  
  // 重新计算相机的投影矩阵，使宽高比变更生效
  ctx.camera?.updateProjectionMatrix();
  
  // 获取设备像素比，用于高DPI屏幕的精确计算
  const pixcel = window.devicePixelRatio;
  
  // 更新视口范围的左上角坐标（考虑设备像素比）
  ctx.viewRange!.minX = wrapperEle.offsetLeft * pixcel;
  ctx.viewRange!.minY = wrapperEle.offsetTop * pixcel;
  
  // 更新视口范围的右下角坐标
  ctx.viewRange!.maxX = ctx.viewRange!.minX + width * pixcel;
  ctx.viewRange!.maxY = ctx.viewRange!.minY + height * pixcel;
  
  // 返回更新后的尺寸信息
  return {
    /** 画布宽度（像素） */
    width,
    /** 画布高度（像素） */
    height,
  };
}
