import type { MOUSE } from "three";
import { POINT_STATUS } from "../enums";
export type CameraConfig = {
    fov?: number;
    aspect?: number;
    near?: number;
    far?: number;
    position?: {
        x: number;
        y: number;
        z: number;
    };
    target?: {
        x: number;
        y: number;
        z: number;
    };
};
export type RendererConfig = {
    /** 宽度 */
    width: number;
    /** 高度 */
    height: number;
    /** webGLRenderer 配置 */
    webGLRenderer?: {
        /** 抗锯齿 */
        antialias: boolean;
    };
    /** css2DRenderer 配置 */
    css2DRenderer?: {
        domElement: HTMLElement;
    };
};
export type Config = {
    camera?: CameraConfig;
    renderer?: RendererConfig;
    levelSpace?: number;
    pointSpace?: number;
};
/**
 *场景模式
 *singleRoot--单个根节点场景
 *multiplyRoots--多个根节点场景
 */
export type ModelPops = "signalRoot" | "multiplyRoots";
/**
 * 领域类型数据
 */
export type AreasData = {
    areaId: string;
    areaName: string;
    points: AreasData[];
};
/**
 *每个知识点的结构
 */
export type PointData = {
    pointId: string;
    pointName: string;
    parentPointIds: string[];
    isMilestone: boolean;
    /**学习进度 */
    status: number;
};
/**
 * 知识点类型数据
 */
export type PointsData = {
    areaId: string;
    pointId: string;
    pointName: string;
    children: PointsData[];
};
/** 点集 */
export type Points = {
    [id: string]: Point;
};
/** 点 */
export interface Point {
    /** 编号 */
    id: string;
    /** 名称 */
    name: string;
    /** 父节点 id */
    parentIds: string[];
    /** 子节点 id */
    childIds: string[];
    /** 层级 */
    level: number;
    /** 坐标 */
    coordinate: [number, number, number];
    /**里程碑 */
    isMilestone: boolean;
    /**学习进度 */
    status: number;
    /**渲染后设置的索引等同于id */
    index?: number;
    /**渲染线后后末节点的索引 */
    endPointsIndex?: number;
}
export interface newPoint extends Point {
    status: POINT_STATUS;
}
/** 边 */
export interface Edge {
    start: Point;
    end: Point;
}
/** 边集 */
export type Edges = {
    [key: string]: Edge;
};
/**
 * 知识点图
 */
export declare class Graph {
    levels: Points[];
    idLevelMap: {
        [id: string]: number;
    };
    edges: Edges;
    bfsLevel: {
        [id: string]: number;
    };
    private pointsData;
    private maxLevel;
    private fullGraph;
    private levelSpace;
    private pointSpace;
    constructor(pointsData: PointData[], maxLevel?: number, levelSpace?: number, pointSpace?: number);
    addPointData(pointsData: PointData[]): void;
    update(args?: {
        pointsData?: PointData[];
        maxLevel?: number;
    }): void;
    private build;
    private planLevel;
    /**根据id获取point */
    getPointById(id: string): Point | null;
    /**获取当前图所有节点总数 */
    getPointsLength(): number;
    /**获取当前图所有节点 */
    getPointsData(): Point[];
}
export declare enum GraphType {
    Point = 0,
    Line = 1
}
/**子图切换数据 */
export type FocusData = {
    focusPoint: Point;
    rootPointId: string;
    points: Point[];
};
export type EventsCallback = {
    loadMoreCallback?: (rootId: string, current: number, levelSize: number) => void;
    clickLabelCallback?: (id: string) => void;
};
export type Data = {
    dataList: PointData[];
    total: number;
};
export type Response<T> = {
    code: string;
    data: T;
    message: string;
    success: boolean;
};
export type getSignalRootApi<T = Data> = (current: number, limit: number, klgCode: string) => Promise<Response<T>>;
export type SceneConfig = {
    /**场景l亮度 */
    backgroundIntensity: number;
    /**场景模糊度 */
    backgroundBlurriness: number;
    /**溯源图整体位置 */
    groupPosition: [number, number, number];
};
export type ControlsConfig = {
    position: [number, number, number];
    target: [number, number, number];
    minPolarAngle: number;
    maxPolarAngle: number;
    minDistance: number;
    maxDistance: number;
    mouseButtons: {
        LEFT: MOUSE;
        MIDDLE: MOUSE;
        RIGHT: MOUSE;
    };
    enableDamping: boolean;
    /**沿y轴移动范围 */
    yMinRange: number;
    yMaxRange: number;
    yDelta: number;
};
/**容器尺寸 */
export type Size = {
    width: number;
    height: number;
};
/**
 * 视口坐标范围
 */
export type ViewRange = {
    minX: number;
    maxX: number;
    minY: number;
    maxY: number;
};
