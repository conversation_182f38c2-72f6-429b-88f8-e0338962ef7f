import { InstancedMesh } from "three";
import { Point } from "../types";
export declare class FocusCrust extends InstancedMesh {
    lastPoint: Point | null;
    constructor(size: number, opacity?: number);
    /**
     * 渲染帧更新函数
     * @param {number} delta 时间间隔
     */
    update(delta?: number): void;
    /**
     * 把该模型绑定在聚焦节点上
     * @param {Point} bindPoint 聚焦节点的userData数据
     */
    display(bindPoint: Point): void;
    /**
     * 隐藏
     */
    hide(): void;
    /**
     * 坐标更新
     * @param position 坐标
     */
    updatePosition(position: [number, number, number]): void;
}
declare const focusCrust: FocusCrust;
export default focusCrust;
