export default function testAPI<T>(current: number, limit: number, klgCode?: String): Promise<T>;
export declare function multiplyRootsAPI<T>(current: number, limit: number, spuId: string): Promise<T>;
/**
 * 本地服务器测试接口-单根节点
 */
export declare function localModeApi<T>(current: number, limit: number, klgCode?: String): Promise<T>;
/**
 * 本地服务器测试接口-多根节点
 */
export declare function localMode2Api<T>(current: number, limit: number, klgCode?: String): Promise<T>;
