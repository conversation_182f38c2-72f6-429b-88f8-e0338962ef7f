export enum GraphType {
  /*点*/
  Point,
  /*线*/
  Line,
}

export enum ENTER_FOCUS_MODE {
  /*进入*/
  ENTER,
  /*返回*/
  BACK,
}

export enum STATUS {
  enter,
  leave,
}

export enum POINT_STATUS {
  default,
  hover,
  focus,
  focusChid,
}

export enum POINT_STUDY_STATUS {
  /*未学习*/
  unstudy,
  /*已学习*/
  studied,
  /*已掌握 */
  mastered,
}

export enum POINT_STUDY_STATUS_COLOR {
  unstudy = 0x5ae0ff,
  studied = 0xffa206,
  mastered = 0xfac2ff,
}

/**
 * 视角模式--聚焦/全局
 */
export enum VIEW_MODE {
  /**聚焦模式-默认 */
  Focus_VIEW,
  /**全局模式 */
  GLOBAL_VIEW,
}

/**
 * 标题label,视口位置class
 */
export enum LABEL_POSITION_CLASS {
  /**右上角 */
  TOP_RIGHT = "top-right",
  /**左上角 */
  TOP_LEFT = "top-left",
  /**右下角 */
  BOTTOM_RIGHT = "bottom-right",
  /**左下角 */
  BOTTOM_LEFT = "bottom-left",
}

//组件使用场景模式
export enum MODE {
  /**单根节点模式场景 */
  Single_ROOT = "signalRoot",
  /**多根节点模式场景 */
  MULTIPLE_ROOT = "multiplyRoots",
}

/**加载状态 */
export enum LOAD_STATUS {
  //加载中
  loading,
  // 加载完成
  loaded,
  // 加载失败
  error,
}

export enum LABEL_POSITION {
  /**左上角 */
  topLeft = "topLeft",
  /**右上角 */
  topRight = "topRight",
  /**左下角 */
  bottomLeft = "bottomLeft",
  /**右下角 */
  bottomRight = "bottomRight",
}
