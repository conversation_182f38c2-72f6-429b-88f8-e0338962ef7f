export declare enum GraphType {
    Point = 0,
    Line = 1
}
export declare enum ENTER_FOCUS_MODE {
    ENTER = 0,
    BACK = 1
}
export declare enum STATUS {
    enter = 0,
    leave = 1
}
export declare enum POINT_STATUS {
    default = 0,
    hover = 1,
    focus = 2,
    focusChid = 3
}
export declare enum POINT_STUDY_STATUS {
    unstudy = 0,
    studied = 1,
    mastered = 2
}
export declare enum POINT_STUDY_STATUS_COLOR {
    unstudy = 5955839,
    studied = 16753158,
    mastered = 16433919
}
/**
 * 视角模式--聚焦/全局
 */
export declare enum VIEW_MODE {
    /**聚焦模式-默认 */
    Focus_VIEW = 0,
    /**全局模式 */
    GLOBAL_VIEW = 1
}
/**
 * 标题label,视口位置class
 */
export declare enum LABEL_POSITION_CLASS {
    /**右上角 */
    TOP_RIGHT = "top-right",
    /**左上角 */
    TOP_LEFT = "top-left",
    /**右下角 */
    BOTTOM_RIGHT = "bottom-right",
    /**左下角 */
    BOTTOM_LEFT = "bottom-left"
}
export declare enum MODE {
    /**单根节点模式场景 */
    Single_ROOT = "signalRoot",
    /**多根节点模式场景 */
    MULTIPLE_ROOT = "multiplyRoots"
}
/**加载状态 */
export declare enum LOAD_STATUS {
    loading = 0,
    loaded = 1,
    error = 2
}
export declare enum LABEL_POSITION {
    /**左上角 */
    topLeft = "topLeft",
    /**右上角 */
    topRight = "topRight",
    /**左下角 */
    bottomLeft = "bottomLeft",
    /**右下角 */
    bottomRight = "bottomRight"
}
