declare global {
  interface Window {
    MathJax: any;
  }
}

// 注入 MathJax
export function injectMathJax() {
  return new Promise((resolve) => {
    if (!window.MathJax) {
      window.MathJax = {
        tex: {
          // 行内公式
          // inlineMath: [["$", "$"]],
          // // 块级公式
          // displayMath: [["$$", "$$"]],
          inlineMath: [
            ["$", "$"],
            ["\\(", "\\)"],
          ], // 允许使用 $...$ 作为行内数学公式
          displayMath: [
            ["$$", "$$"],
            ["\\[", "\\]"],
          ],
        },
        options: {
          skipHtmlTags: [
            //  HTML tags that won't be searched for math
            "script",
            "noscript",
            "style",
            "textarea",
            "pre",
            "code",
            "annotation",
            "annotation-xml",
          ],
        },
        startup: {
          pageReady: () => {
            return window.MathJax.startup.defaultPageReady().then(() => {
              resolve(null);
            });
          },
        },
        svg: {
          fontCache: "global",
        },
      };
      const script = document.createElement("script");
      script.src =
        // "https://cdnjs.cloudflare.com/ajax/libs/mathjax/3.2.0/es5/tex-mml-chtml.js";
        "https://cdn.bootcdn.net/ajax/libs/mathjax/3.2.0/es5/tex-mml-chtml.js"; //国内镜像源
      script.async = true;
      document.head.appendChild(script);
      console.log("MathJax is loading");
    } else {
      resolve(null);
    }
  });
}

// 渲染公式
export function renderMathJax(el: Element): Promise<any> {
  if (!window.MathJax.version) {
    console.warn("MathJax is not loaded yet");
    return Promise.resolve(null);
  }
  return window.MathJax.typesetPromise([el]);
}

/**
 * 重新格式化包含公式的标题，由于公式库是2.x版本，现在使用3.x版本，所以需要重新格式化。
 * 本质上就是把script标签字符串变为'$'
 * @param {string} title 包含公式的标题
 * @returns {string} 格式化后的标题
 */
export function reFormateTitle(title: string) {
  return title.replace(
    /<script type=['"]math\/tex['"]>(.*?)<\/script>/g,
    (str) => {
      str = str.replace(/<script type=['"]math\/tex['"]>/, "");
      str = str.replace(/<\/script>/, "");
      return "$" + str + "$";
    }
  );
}
