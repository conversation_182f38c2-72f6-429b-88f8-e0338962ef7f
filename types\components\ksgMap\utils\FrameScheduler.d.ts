/**
 * 分帧处理调度器
 */
export default class FrameScheduler {
    private taskQueue;
    private isRunning;
    completedCallback: () => void;
    /**
     *添加任务
     * @param task 任务
     */
    addTask(task: () => boolean): void;
    /**
     * 分帧计算函数，此函数在渲染帧中调用，调用一次择执行一次处理任务
     */
    runNextTask(): void;
    /**
     * 清空任务
     */
    clearTasks(): void;
    /**
     *执行完成
     */
    onCompleted(callback: () => void): void;
}
