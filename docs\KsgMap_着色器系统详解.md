# KsgMap 着色器系统详解

## 🎨 Shader是什么？

**Shader（着色器）** 是运行在GPU上的小程序，用于控制3D图形的渲染过程。它们可以用GLSL（OpenGL Shading Language）编写，直接在显卡上执行，速度极快。

### 两种主要类型：

1. **顶点着色器（Vertex Shader）** - 处理每个顶点的位置、大小等属性
2. **片段着色器（Fragment Shader）** - 处理每个像素的颜色、透明度等

## 🔍 项目中的Shader文件分析
###  流程图
- Shader在渲染管线中的位置
```mermaid
graph TD
    A[CPU - JavaScript] --> B[顶点数据<br/>BufferAttribute]
    B --> C[顶点着色器<br/>Vertex Shader]
    C --> D[图元装配<br/>三角形/点/线]
    D --> E[光栅化<br/>生成像素]
    E --> F[片段着色器<br/>Fragment Shader]
    F --> G[帧缓冲区<br/>最终画面]
    
    H[纹理贴图] --> F
    I[Uniform变量<br/>时间/颜色等] --> C
    I --> F
    

```

- 项目中Shader的使用架构
```mermaid
graph TD
    A[JavaScript代码] --> B[导入Shader文件]
    B --> C[创建ShaderMaterial]
    C --> D[设置Uniform变量]
    C --> E[设置Attribute属性]
    D --> F[GPU执行着色器]
    E --> F
    F --> G[渲染到屏幕]
    
    H[pointVert.glsl<br/>节点顶点着色器] --> C
    I[pointFrag.glsl<br/>节点片段着色器] --> C
    J[lineVert.glsl<br/>连线顶点着色器] --> C
    K[lineFrag.glsl<br/>连线片段着色器] --> C
    L[haloVert.glsl<br/>光晕顶点着色器] --> C
    M[haloFrag.glsl<br/>光晕片段着色器] --> C
    
```
### 1. **节点着色器** (pointVert.glsl + pointFrag.glsl)

**作用**：控制知识节点的视觉效果

````typescript path=src/components/ksgMap/core/KsgPoints.ts mode=EXCERPT
// 导入着色器文件
import vertShader from "../shader/pointVert.glsl?raw";
import fragShader from "../shader/pointFrag.glsl?raw";

// 创建ShaderMaterial
new ShaderMaterial({
  uniforms: {
    map: { value: starTexture },        // 纹理贴图
    uTime: { value: Math.random() },    // 时间参数
    offset: { value: [-0.02, -0.02] },  // 纹理偏移
  },
  vertexShader: vertShader,   // 顶点着色器
  fragmentShader: fragShader, // 片段着色器
})
````

**实现的效果**：
- ✨ **呼吸动画** - 节点周期性透明度变化
- 🎯 **距离缩放** - 根据相机距离调整节点大小
- 🎨 **动态颜色** - 根据学习状态显示不同颜色
- 🖼️ **纹理贴图** - 使用图片纹理美化节点

### 2. **连线着色器** (lineVert.glsl + lineFrag.glsl)

**作用**：实现连线的流光效果

````typescript path=src/components/ksgMap/core/KsgLine.ts mode=EXCERPT
const material = new ShaderMaterial({
  uniforms: {
    uOpacity: { value: opacity },      // 基础透明度
    uTime: { value: 0 },              // 动画时间
    uWidth: { value: 0.4 },           // 流光宽度
    uSpeed: { value: 0.15 },          // 流光速度
    uProgress: { value: -0.3 },       // 流光进度
    uIsRandom: { value: true },       // 是否随机
  },
});
````

**实现的效果**：
- 🌊 **流光动画** - 沿连线移动的光效
- 🎲 **随机效果** - 多条线的流光随机化
- 💫 **渐变透明** - 流光区域高亮，其他区域半透明

### 3. **光晕着色器** (haloVert.glsl + haloFrag.glsl)

**作用**：节点悬停时的光晕效果

````typescript path=src/components/ksgMap/core/KsgHover.ts mode=EXCERPT
new ShaderMaterial({
  uniforms: {
    uTime: { value: 0.0 },
    uSpeed: { value: 0.5 },           // 扩散速度
    uNumRings: { value: 10.0 },       // 圆环数量
    uRingDuration: { value: 2.0 },    // 圆环持续时间
    uColor: { value: new Color(0xffffff) },
  },
})
````

**实现的效果**：
- 🎯 **扩散圆环** - 从中心向外扩散的圆环
- ⏰ **时间控制** - 基于时间的动画循环
- 🌈 **颜色变化** - 根据节点状态改变颜色

## 🚀 如何使用Shader

### 📝 基本使用模式

```typescript
// 1. 导入着色器文件
import vertexShader from "./shader/myVert.glsl?raw";
import fragmentShader from "./shader/myFrag.glsl?raw";

// 2. 创建ShaderMaterial
const material = new ShaderMaterial({
  // 3. 设置uniform变量（从JavaScript传递给着色器的参数）
  uniforms: {
    uTime: { value: 0 },              // 时间
    uColor: { value: new Color(0xff0000) }, // 颜色
    uOpacity: { value: 1.0 },         // 透明度
  },
  
  // 4. 指定着色器
  vertexShader: vertexShader,
  fragmentShader: fragmentShader,
  
  // 5. 其他材质属性
  transparent: true,
  depthWrite: false,
});

// 6. 在渲染循环中更新uniform
function animate() {
  material.uniforms.uTime.value += 0.01;
  requestAnimationFrame(animate);
}
```

### 🔧 修改Shader效果的方法

#### 1. **修改颜色**
```glsl
// 在片段着色器中
void main() {
    // 原来：使用传入的颜色
    gl_FragColor = vec4(vColor, 1.0);
    
    // 修改：固定红色
    gl_FragColor = vec4(1.0, 0.0, 0.0, 1.0);
    
    // 修改：基于时间的彩虹色
    vec3 rainbow = vec3(
        sin(uTime) * 0.5 + 0.5,
        sin(uTime + 2.0) * 0.5 + 0.5,
        sin(uTime + 4.0) * 0.5 + 0.5
    );
    gl_FragColor = vec4(rainbow, 1.0);
}
```

#### 2. **修改动画速度**
```typescript
// 在JavaScript中修改uniform
material.uniforms.uSpeed.value = 2.0; // 加快速度
material.uniforms.uTime.value += 0.02; // 加快时间流逝
```

#### 3. **添加新效果**
```glsl
// 在片段着色器中添加闪烁效果
void main() {
    float blink = sin(uTime * 10.0) * 0.5 + 0.5; // 快速闪烁
    vec4 color = texture2D(map, gl_PointCoord);
    gl_FragColor = vec4(color.rgb, color.a * blink);
}
```

## 💡 实战示例：自定义节点效果

### 创建脉冲效果的节点

```glsl
// 自定义片段着色器
varying vec3 vColor;
varying float vOpacity;
uniform float uTime;

void main() {
    vec2 center = gl_PointCoord - 0.5;
    float dist = length(center);
    
    // 脉冲效果：基于距离和时间的波动
    float pulse = sin(dist * 20.0 - uTime * 5.0) * 0.5 + 0.5;
    
    // 圆形遮罩
    float mask = 1.0 - smoothstep(0.4, 0.5, dist);
    
    // 最终颜色
    vec3 finalColor = vColor * pulse;
    float finalAlpha = vOpacity * mask;
    
    gl_FragColor = vec4(finalColor, finalAlpha);
}
```

```typescript
// 在JavaScript中使用
const customMaterial = new ShaderMaterial({
  uniforms: {
    uTime: { value: 0 },
  },
  vertexShader: customVertexShader,
  fragmentShader: customFragmentShader,
  transparent: true,
});

// 动画更新
function animate() {
  customMaterial.uniforms.uTime.value += 0.01;
}
```

## 🎯 Shader的核心优势

1. **高性能** - 在GPU上并行执行，处理大量节点时性能优异
2. **灵活性** - 可以实现任意复杂的视觉效果
3. **实时性** - 支持实时动画和交互效果
4. **可定制** - 完全控制渲染过程的每个细节

## 🔍 调试Shader的技巧

1. **使用简单颜色测试**
```glsl
void main() {
    gl_FragColor = vec4(1.0, 0.0, 0.0, 1.0); // 纯红色测试
}
```

2. **可视化变量**
```glsl
void main() {
    // 将时间值可视化为颜色
    float t = sin(uTime) * 0.5 + 0.5;
    gl_FragColor = vec4(t, t, t, 1.0);
}
```

3. **分步骤验证**
```glsl
void main() {
    vec2 uv = gl_PointCoord;
    gl_FragColor = vec4(uv, 0.0, 1.0); // 显示UV坐标
}
```

通过理解这些Shader的作用和使用方式，你就可以自由地修改和扩展项目中的视觉效果了！Shader是实现炫酷3D效果的核心技术。

## 概述

KsgMap 项目使用自定义 GLSL 着色器来实现高性能的节点渲染和特殊视觉效果。着色器系统是整个项目的核心技术之一，负责在 GPU 上并行处理大量的图形数据，实现流光动画、呼吸效果、光晕扩散等复杂的视觉特效。

## 着色器架构

### 着色器文件组织

```
shader/
├── pointVert.glsl      # 节点顶点着色器
├── pointFrag.glsl      # 节点片段着色器
├── lineVert.glsl       # 连线顶点着色器
├── lineFrag.glsl       # 连线片段着色器
├── haloVert.glsl       # 光晕顶点着色器
├── haloFrag.glsl       # 光晕片段着色器
├── vert.glsl           # 通用顶点着色器
└── frag.glsl           # 通用片段着色器
```

## 节点着色器系统

### 顶点着色器 (pointVert.glsl)

**主要功能**：
- 处理节点的位置变换
- 计算点的屏幕大小
- 传递自定义属性到片段着色器

**关键代码解析**：
```glsl
attribute float size;              // 节点大小
attribute vec3 customColor;        // 自定义颜色
attribute float customOpacity;     // 自定义透明度
attribute float customRandom;      // 随机值（用于动画）
attribute float breathStatus;      // 呼吸动画状态

varying vec3 vColor;               // 传递给片段着色器的颜色
varying float vOpacity;            // 传递给片段着色器的透明度
varying float vCustomRandom;       // 传递给片段着色器的随机值
varying float vIsBreathAni;        // 传递给片段着色器的呼吸状态

void main() {
    vColor = customColor;
    vOpacity = customOpacity;
    vCustomRandom = customRandom;
    vIsBreathAni = breathStatus;
    
    // 计算模型视图位置
    vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
    
    // 根据距离计算点的大小（透视效果）
    gl_PointSize = 10.0 * (350.0 / -mvPosition.z);
    
    // 计算最终位置
    gl_Position = projectionMatrix * mvPosition;
}
```

### 片段着色器 (pointFrag.glsl)

**主要功能**：
- 纹理采样和颜色混合
- 呼吸动画效果实现
- 透明度处理和边缘柔化

**呼吸动画实现**：
```glsl
uniform float uTime;               // 时间变量
uniform sampler2D map;             // 纹理贴图
uniform sampler2D alphaMap;        // Alpha 贴图
uniform vec2 offset;               // 纹理偏移

varying vec3 vColor;
varying float vOpacity;
varying float vCustomRandom;
varying float vIsBreathAni;

void main() {
    // 采样纹理
    vec4 texColor = texture2D(map, gl_PointCoord + offset);
    float alpha = texture2D(alphaMap, gl_PointCoord + offset).a;
    
    // 呼吸动画效果
    if(vIsBreathAni == 1.0) {
        // 使用正弦函数创建呼吸效果
        float finalOpacity = sin(uTime + vCustomRandom) * 0.4 + 0.6;
        vec4 finalColor = vec4(vColor * texColor.rgb, finalOpacity);
        gl_FragColor = finalColor;
    } else {
        // 普通渲染
        vec4 finalColor = vec4(vColor * texColor.rgb, texColor.a * alpha * vOpacity);
        gl_FragColor = finalColor;
    }
}
```

## 连线着色器系统

### 顶点着色器 (lineVert.glsl)

**数据传递**：
```glsl
attribute float lineIndex;         // 线条索引
attribute float segmentProgress;   // 线段进度 (0-1)
attribute float random;            // 随机值

varying vec3 vColor;               // 颜色
varying float vIndex;              // 线条索引
varying vec3 vPosition;            // 位置
varying float vSegmentProgress;    // 线段进度
varying float vCustomRandom;       // 随机值

void main() {
    vColor = color;
    vIndex = lineIndex;
    vPosition = position;
    vSegmentProgress = segmentProgress;
    vCustomRandom = random;
    
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
}
```

### 片段着色器 (lineFrag.glsl)

**流光动画核心算法**：
```glsl
uniform float uOpacity;            // 基础透明度
uniform float uTime;               // 时间
uniform float uWidth;              // 流光宽度 (0.0 - 1.0)
uniform float uSpeed;              // 流光速度 (0.0 - 1.0)
uniform float uOpacityOffset;      // 流光透明度偏移
uniform float uProgress;           // 流光进度
uniform bool uIsRandom;            // 是否随机流光

varying float vIndex;
varying vec3 vColor;
varying float vSegmentProgress;
varying float vCustomRandom;

// 随机进度计算函数
float progressUnderRandom(float randomStart, float ux) {
    float x = randomStart + ux;
    return (2.0 + 1.5) * (mod(x, 3.5) / 3.5) - 1.5;
}

void main() {
    // 计算当前流光位置
    float progress = uIsRandom ? 
        progressUnderRandom(vCustomRandom, uProgress) : uProgress;
    
    // 计算流光范围
    float halfWidth = uWidth / 2.0;
    
    // 使用 smoothstep 创建平滑的流光效果
    float alpha = smoothstep(progress - halfWidth, progress, vSegmentProgress)
                * (1.0 - smoothstep(progress, progress + halfWidth, vSegmentProgress));
    
    // 混合基础透明度和流光透明度
    float finalOpacity = mix(uOpacity, uOpacity + uOpacityOffset, alpha);
    
    gl_FragColor = vec4(vColor, finalOpacity);
}
```

## 光晕着色器系统

### 光晕片段着色器 (haloFrag.glsl)

**多圆环扩散效果**：
```glsl
uniform float uTime;               // 时间
uniform vec2 uSpriteSize;          // 精灵大小
uniform float uSpeed;              // 扩散速度
uniform float uMinRadius;          // 最小半径
uniform int uNumRings;             // 圆环数量
uniform float uRingDuration;       // 单个圆环持续时间
uniform vec3 uColor;               // 颜色
uniform float uDelay;              // 圆环间延迟

varying vec2 vUv;

void main() {
    vec2 uv = vUv - 0.5;
    float maxRadius = min(uSpriteSize.x, uSpriteSize.y) * 0.5;
    float distFromCenter = length(uv) * min(uSpriteSize.x, uSpriteSize.y);
    
    vec4 ringColor = vec4(uColor, 0.7);
    vec4 totalColor = vec4(0.0);
    
    float currentTime = uTime * uSpeed;
    float totalDuration = uRingDuration + float(uNumRings - 1) * uDelay;
    float cycleTime = mod(currentTime, totalDuration);
    
    // 渲染多个圆环
    for(int i = 0; i < uNumRings; i++) {
        float ringStart = float(i) * uDelay;
        float ringEnd = ringStart + uRingDuration;
        
        if (cycleTime >= ringStart && cycleTime < ringEnd) {
            float ringTimer = clamp(cycleTime - ringStart, 0.0, uRingDuration);
            
            // 计算圆环半径
            float ringRadius = mix(uMinRadius / min(uSpriteSize.x, uSpriteSize.y), 
                                 maxRadius, ringTimer / uRingDuration);
            
            float innerEdge = ringRadius - 0.01;
            float outerEdge = ringRadius + 0.01;
            
            // 创建圆环形状
            float ring = smoothstep(outerEdge, innerEdge, distFromCenter);
            
            // 应用衰减效果
            float attenuation = exp(-pow(distFromCenter - ringRadius, 2.0) * 100.0) 
                              * (1.0 - ringTimer / uRingDuration);
            
            vec4 currentRingColor = ringColor * ring * attenuation;
            totalColor += currentRingColor;
        }
    }
    
    gl_FragColor = clamp(totalColor, 0.0, 0.5);
}
```

## 着色器优化技术

### 1. 性能优化

**减少分支语句**：
```glsl
// 避免使用 if-else，使用数学函数替代
float result = mix(valueA, valueB, condition);
```

**预计算常量**：
```glsl
// 在 CPU 端预计算，通过 uniform 传递
uniform float precomputedValue;
```

**纹理优化**：
```glsl
// 使用合适的纹理格式和大小
// 避免在片段着色器中进行复杂计算
```

### 2. 内存优化

**属性复用**：
```glsl
// 将多个小数值打包到一个 vec4 中
attribute vec4 packedData; // x: size, y: opacity, z: random, w: status
```

**Varying 变量优化**：
```glsl
// 只传递必要的数据到片段着色器
// 在顶点着色器中进行尽可能多的计算
```

### 3. 视觉质量优化

**抗锯齿处理**：
```glsl
// 使用 smoothstep 创建平滑边缘
float edge = smoothstep(0.0, fwidth(dist), dist);
```

**颜色空间处理**：
```glsl
// 在线性空间中进行颜色计算
// 最后转换到 sRGB 空间
```

## 着色器调试技巧

### 1. 可视化调试

**颜色编码**：
```glsl
// 将数值映射到颜色进行可视化
gl_FragColor = vec4(debugValue, 0.0, 0.0, 1.0);
```

**分步验证**：
```glsl
// 逐步验证计算结果
// gl_FragColor = vec4(step1, step1, step1, 1.0);
// gl_FragColor = vec4(step2, step2, step2, 1.0);
```

### 2. 性能分析

**简化测试**：
```glsl
// 临时简化复杂计算，测试性能影响
// gl_FragColor = vec4(1.0, 0.0, 0.0, 1.0); // 简化版本
```

**批量测试**：
```glsl
// 测试不同的算法实现
// 比较性能和视觉效果
```

## 着色器与 JavaScript 的交互

### Uniform 变量更新

```typescript
// 在渲染循环中更新 uniform 变量
material.uniforms.uTime.value = clock.getElapsedTime();
material.uniforms.uProgress.value += speed;
```

### 属性数据更新

```typescript
// 更新顶点属性
const colorAttribute = geometry.getAttribute('customColor');
colorAttribute.setXYZ(index, color.r, color.g, color.b);
colorAttribute.needsUpdate = true;
```

### 动态着色器编译

```typescript
// 根据配置动态生成着色器代码
const fragmentShader = generateFragmentShader(options);
const material = new ShaderMaterial({
    fragmentShader,
    // ...其他配置
});
```

## 总结

KsgMap 的着色器系统通过精心设计的 GLSL 程序，实现了高性能的大规模节点渲染和丰富的视觉效果。关键技术包括：

1. **模块化设计**：不同功能的着色器分离，便于维护和扩展
2. **高效算法**：优化的数学计算和渲染流程
3. **动画系统**：基于时间和随机值的动态效果
4. **性能优化**：减少分支、预计算、纹理优化等技术
5. **调试支持**：完善的调试和性能分析方法

这套着色器系统为 KsgMap 提供了强大的视觉表现力和优异的性能表现，是整个项目技术架构的重要组成部分。
