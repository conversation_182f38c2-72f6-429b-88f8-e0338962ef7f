import { Mesh, Line } from "three";
import KsgLine from "../core/KsgLine";
import KsgPoint from "../core/KsgPoints";
import { Point } from "../types";
type Option = {
    opacity: number;
    x: number;
    y: number;
    z: number;
};
/**
 * 初始化知识节点时渲染动画
 */
export declare function pointEnterAnimation(point: Point, pointsMesh: KsgPoint, option: Option, duration?: number): Promise<unknown>;
/**
 *初始化连线时加载动画
 *@param line 连线
 *@param startPoint 起点
 *@param endPoint 终点
 *@param opacity 透明度
 *@param duration 动画时长
 */
export declare function lineEnterAnimation(line: KsgLine, startPoint: Point, endPoint: Point, opacity: number, duration?: number): Promise<unknown>;
/**
 * 移除知识节点动画
 */
export declare function pointLeaveAnimation(mesh: any, duration?: number, option?: {
    opacity: number;
    scale: number;
}): Promise<any>;
/**
 * 移除连线动画
 */
export declare function lineLeaveAnimation(line: KsgLine, duration?: number): Promise<unknown>;
export declare function getCrtLineOpacity(line: Line | Mesh): number;
export {};
