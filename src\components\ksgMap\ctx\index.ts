import { Scene, PerspectiveCamera, Group, WebG<PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "three";
// import type { Graph } from "../types";
import {
  CSS2DRenderer,
  // CSS2DObject,
} from "three/examples/jsm/renderers/CSS2DRenderer";
import { KsgControls } from "../core/KsgControls";
// import { CustomEffectComposer } from "../core/bloomEffect";
import { VIEW_MODE, MODE, LOAD_STATUS } from "../enums";
import KsgPoint from "../core/KsgPoints";
import KsgLine from "../core/KsgLine";
import KsgGraph from "../core/KsgGraph";
const ctx: Partial<Context> = {
  /*默认单根节点 */
  model: MODE.Single_ROOT,
  loadStatus: LOAD_STATUS.loaded,
  levelSpace: 20,
  viewMode: VIEW_MODE.Focus_VIEW,
  focusPointInfo: {
    pointId: "",
  },
  pointsLevelPager: {
    current: 1,
    levelSize: 1,
    total: Infinity,
  },
  focusStack: [],
  focusStackMaxSize: 15,
  focusBack: () => {},
  focusBackToRoot: () => {},
  isControls: false,
  viewRange: {
    minX: 0,
    maxX: 0,
    minY: 0,
    maxY: 0,
  },
  maxDistance: 110,
};
export default ctx;
/**
 * 全局上下文
 */
export type Context = {
  /**模式 */
  model: MODE;
  //three场景
  scene: Scene;
  // 装载知识节点图的容器
  viewGroup: Group;
  camera: PerspectiveCamera;
  renderer: WebGLRenderer;
  css2dRenderer: CSS2DRenderer;
  composer: any;
  controls: KsgControls;
  /**是不是在控制单中 */
  isControls: boolean;
  /**视角模式 */
  viewMode: VIEW_MODE;
  // 知识节点的数据结构
  graph: KsgGraph;
  // 当前聚焦节点
  focusPointInfo: {
    pointId: string;
  };
  // 知识节点查询
  pointsLevelPager: {
    // 当前层级，
    current: number;
    // 层数
    levelSize: number;
    //总层数
    total: number;
  };
  // 知识节点的边数据结构
  edge: any;
  /*层级间隔距离*/
  levelSpace: number;
  /*当前渲染最大层级y坐标*/
  maxLevelY: number;
  /*是否在加载更多数据状态 */
  loadStatus: LOAD_STATUS;
  /*知识节点相关配置 */
  point: {
    //节点半径
    radius: number;
    //节点间距
    space: number;
  };
  /**连线流光效果相关配置*/
  line: {
    //流光长度
    length: number;
    // 流光移动速度
    speed: number;
    // 多条连线流光效果是否随机
    isRandom: boolean;
  };
  /*hover弹窗xy轴偏移配置 */
  hoverLabel: {
    //x轴偏移量
    offsetX: number;
    //y轴偏移量
    offsetY: number;
  };
  /*弹窗显示距离限制 */
  maxDistance: number;
  /*点之间的间距*/
  pointSpace: number;
  /**子图历史栈 */
  focusStack: string[];
  /**最大历史记录数 */
  focusStackMaxSize: number;
  /*回到根节点*/
  focusBackToRoot: () => void;
  /*子图回退 */
  focusBack: () => void;
  /**视口范围 */
  viewRange: {
    minX: number;
    maxX: number;
    minY: number;
    maxY: number;
  };
  /*渲染时的节点数据 */
  pointsMesh: KsgPoint;
  /*聚焦的连线 */
  focusLine: KsgLine;
  /*聚焦节点直接前驱节点所有索引 */
  focusChildrenIndex: Set<number>;
};
