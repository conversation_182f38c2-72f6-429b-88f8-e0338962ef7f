    varying float vIntensity;
    varying vec2 vUv;
    uniform sampler2D uNoiseTexture;
    uniform vec3 uColor;
    uniform float uTime;
    uniform float opacity;
    void main() {
        //默认透明度
       
        vec2 nuv = vUv;

        nuv.xy += uTime * 0.3;

        vec4 noiseColor = texture2D(uNoiseTexture, nuv);

        gl_FragColor = vec4(noiseColor.rgb * vIntensity * uColor * 0.9,opacity);
    }







