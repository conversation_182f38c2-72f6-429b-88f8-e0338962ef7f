import { EventDispatcher, PerspectiveCamera, MOUSE, TOUCH, Vector3, type BaseEvent, Group, Object3D } from "three";
declare enum KsgMode {
    /** 知识点层 */
    Star = 0,
    /** 领域层 */
    Domain = 1
}
declare class KsgControls extends EventDispatcher<{
    [key: string]: BaseEvent;
}> {
    /** 相机 */
    object: PerspectiveCamera;
    /** 根领域 */
    rootAreaObj: Group;
    subareas: Object3D | null;
    /** The HTML element used for event listeners */
    domElement: HTMLElement;
    /** 是否启用 */
    enabled: boolean;
    /** 相机焦点 */
    target: Vector3;
    /** 相机距离焦点的最小距离 */
    minDistance: number;
    /** 相机距离焦点的最大距离 */
    maxDistance: number;
    /** phi 最小角度 */
    minPolarAngle: number;
    /** phi 最大角度 */
    maxPolarAngle: number;
    /** 启用阻尼 */
    enableDamping: boolean;
    /** 阻尼系数 */
    dampingFactor: number;
    /** 是否启用缩放 */
    enableZoom: boolean;
    /** 缩放速度 */
    zoomSpeed: number;
    /** 是否启用旋转 */
    enableRotate: boolean;
    /** 旋转速度 */
    rotateSpeed: number;
    /** 是否启用平移 */
    enablePan: boolean;
    /** 平移速度 */
    panSpeed: number;
    /** 当前模式 */
    mode: KsgMode;
    /** 键盘按键 */
    keys: {
        LEFT: string;
        UP: string;
        RIGHT: string;
        BOTTOM: string;
    };
    /** 鼠标按键 */
    mouseButtons: {
        LEFT: MOUSE;
        MIDDLE: MOUSE;
        RIGHT: MOUSE;
    };
    /** 触摸事件 */
    touches: {
        ONE: TOUCH;
        TWO: TOUCH;
    };
    /** pixels moved per arrow key push */
    keyPanSpeed: number;
    /** camera y 轴最小范围 */
    yMinRange: number;
    /** camera y 轴最大范围 */
    yMaxRange: number;
    /** target y 轴范围为 [yMinRange, yMaxRange - yDelta] */
    yDelta: number;
    /** 相机焦点 y 轴 */
    yAxis: Vector3;
    /**自动旋转 */
    autoRotate: boolean;
    /** 自动旋转速度 */
    autoRotateSpeed: number;
    /**是否在控制状态中 */
    isControls: boolean;
    controlsTimer: number | NodeJS.Timeout | null;
    _domElementKeyEvents: HTMLElement | null;
    update: (deltaTime?: number | null) => boolean;
    dispose: () => void;
    changeMode: (mode: KsgMode) => void;
    constructor(object: PerspectiveCamera, rootArea: Group, domElement: HTMLElement);
    /**
     * 自动旋转函数,若要开启自动旋转功能,请设置autoRotate为true
     * 渲染帧中调用此方法
     */
    autoRotateUpdate(deltaTime: number): void;
}
export { KsgMode, KsgControls };
