import { type FocusData } from "../types";
import type { DiffData } from "./KsgGraph";
/**
 * 第一次渲染单根节点数据（且聚焦根节点）
 */
export declare function firstRenderSignalRootPoints(): void;
/**
 * 第一次渲染多根节点数据（没有焦点）
 */
export declare function firstRenderMultiplyRootPoints(): void;
/**
 * 加载更多-渲染数据
 */
export declare function renderMoreData(focusIndex: number, diffData: DiffData): void;
/**
 *进入子图
 * @param focusInfo 聚焦节点信息
 */
export declare function renderFocusData(focusInfo: FocusData): Promise<void>;
