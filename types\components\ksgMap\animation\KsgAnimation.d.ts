type AnimationParams = {
    string: number;
};
type AddAnimationOption = {
    from: object;
    to: {
        string: number;
    };
    updateCallback: (animationParams: AnimationParams) => void;
    duration: number;
};
/**
 *该类是为了高效的组织动画提高渲染效率
 */
export declare class KsgAnimation {
    private tweenPool;
    constructor();
    /**
     * 添加一个动画
     */
    addAnimation(options: AddAnimationOption): void;
    /**
     * 更新函数
     * @param {number} time 当前时间
     */
    update(time: number): void;
    /**
     * 暂停函数
     */
    pause(): void;
    /**
     * 恢复函数
     */
    resume(): void;
}
export {};
