import ctx from "../ctx/index";
import {
  Vector2,
  Object3D,
  PerspectiveCamera,
  Raycaster,
  Group,
  Vector3,
  Color,
} from "three";
import {
  GraphType,
  POINT_STATUS,
  STATUS,
  POINT_STUDY_STATUS,
  POINT_STUDY_STATUS_COLOR,
} from "../enums";

// import { Line2 } from "three/examples/jsm/lines/Line2";
// import { LineGeometry } from "three/examples/jsm/lines/LineGeometry";
// import { LineMaterial } from "three/examples/jsm/lines/LineMaterial";
import KsgGraph from "../core/KsgGraph";

/**
 * 为数值添加CSS单位
 * 如果传入的值已经包含单位（px、%、rem、em），则直接返回
 * 否则为纯数字添加px单位
 * @param value 需要添加单位的值，可以是字符串或数字
 * @returns 带有单位的字符串
 */
export function addUnit(value: string | number = 100) {
  if (typeof value === "string" && /px|%|rem|em$/.test(value)) return value;
  return value + "px";
}

/**
 * 根据点的学习状态获取对应的RGB颜色值
 * 将十六进制颜色值转换为Three.js着色器所需的RGB数组格式（0-1范围）
 * @param pointStatus 点的学习状态枚举值
 * @returns RGB数组，每个分量的值在0-1之间
 */
export function pointStatusToColor(pointStatus: POINT_STUDY_STATUS) {
  switch (pointStatus) {
    case POINT_STUDY_STATUS.mastered:
      return hexToRgb(POINT_STUDY_STATUS_COLOR.mastered.toString(16));
    case POINT_STUDY_STATUS.studied:
      return hexToRgb(POINT_STUDY_STATUS_COLOR.studied.toString(16));
    case POINT_STUDY_STATUS.unstudy:
      return hexToRgb(POINT_STUDY_STATUS_COLOR.unstudy.toString(16));
  }
}

/**
 * 将十六进制颜色字符串转换为RGB数组
 * @param hex 十六进制颜色字符串（不包含#号）
 * @returns RGB数组，每个分量的值在0-1之间，适用于Three.js
 */
function hexToRgb(hex: string): [number, number, number] {
  const sliceArr = [
    hex.substring(0, 2), // Red分量
    hex.substring(2, 4), // Green分量
    hex.substring(4, 6), // Blue分量
  ];
  return sliceArr.map((item) => parseInt("0x" + item) / 255) as [
    number,
    number,
    number
  ];
}

/** 控制器相关辅助函数 */

/**
 * 获取鼠标点击区域对应的3D对象ID
 * 使用射线投射检测鼠标点击位置对应的3D对象
 * @param event 鼠标事件对象
 * @param dom Canvas DOM元素
 * @param subareas 包含可点击子区域的3D对象组
 * @param camera 透视相机对象
 * @returns 点击对象的ID，如果未点击到有效对象则返回null
 */
export function getClickArea(
  event: MouseEvent,
  dom: HTMLCanvasElement,
  subareas: Object3D,
  camera: PerspectiveCamera
) {
  // 将屏幕坐标转换为标准化设备坐标(-1到1)
  const x =
    ((event.clientX - dom.getBoundingClientRect().left) / dom.offsetWidth) * 2 -
    1;
  const y =
    -((event.clientY - dom.getBoundingClientRect().top) / dom.offsetHeight) *
      2 +
    1;

  // 创建射线投射器并设置射线方向
  const raycaster = new Raycaster();
  raycaster.setFromCamera(new Vector2(x, y), camera);

  // 检测与子区域的交集
  const intersects = raycaster.intersectObjects(subareas.children, true);
  if (intersects.length > 0) {
    let obj: Object3D | null = intersects[0].object;
    // 向上遍历父对象，查找包含ID的对象
    while (obj && obj.uuid != subareas.uuid) {
      if (obj.userData.id) {
        return obj.userData.id;
      }
      obj = obj.parent;
    }
  }
  return null;
}

/**
 * 线性映射函数
 * 将值从输入范围映射到输出范围
 * @param v 要映射的值
 * @param i1 输入范围的最小值
 * @param i2 输入范围的最大值
 * @param o1 输出范围的最小值
 * @param o2 输出范围的最大值
 * @returns 映射后的值
 */
export function map(v: number, i1: number, i2: number, o1: number, o2: number) {
  return o1 + ((o2 - o1) * (v - i1)) / (i2 - i1);
}

/**
 * 约束函数，将值限制在指定范围内
 * @param v 要约束的值
 * @param min 最小值
 * @param max 最大值
 * @returns 约束后的值
 */
export function constrain(v: number, min: number, max: number) {
  if (v < min) v = min;
  else if (v > max) v = max;
  return v;
}

/**
 * 分段线性映射函数
 * 将一个数值从一个范围映射到另一个范围，支持多段映射
 *
 * 使用线性插值将输入值 `val` 从 `from` 数组定义的范围映射到 `to` 数组定义的范围
 * `from` 和 `to` 数组必须具有相同的长度，并且至少包含两个元素
 * `from` 数组必须单调递增
 *
 * @param val 要映射的值
 * @param from 输入范围数组，必须单调递增
 * @param to 输出范围数组，与from数组一一对应
 * @returns 映射后的值
 * @throws 当from和to数组长度不一致或长度小于2时抛出错误
 */
export function cmap(val: number, from: number[], to: number[]): number {
  if (from.length !== to.length) {
    throw new Error("The 'from' and 'to' arrays must have the same length.");
  }

  if (from.length < 2) {
    throw new Error(
      "The 'from' and 'to' arrays must contain at least two elements for interpolation."
    );
  }

  // 在from数组中查找合适的区间段
  for (let i = 0; i < from.length - 1; i++) {
    if (val >= from[i] && val <= from[i + 1]) {
      // 执行线性插值
      const t = (val - from[i]) / (from[i + 1] - from[i]);
      return to[i] + t * (to[i + 1] - to[i]);
    }
  }

  // 处理边界情况
  if (val < from[0]) {
    return to[0];
  } else {
    return to[to.length - 1];
  }
}

/**
 * 判断两个数字是否近似相等
 * 使用小的误差值来比较浮点数，避免精度问题
 * @param a 第一个数字
 * @param b 第二个数字
 * @returns 如果两个数字的差值小于0.0001则返回true
 */
export function equiv(a: number, b: number) {
  return Math.abs(a - b) < 0.0001;
}

/**
 * 设置嵌套对象属性
 * 根据点分隔的路径字符串设置对象的嵌套属性值
 * 如果路径中的对象不存在，会自动创建
 * @param obj 目标对象
 * @param path 属性路径，使用点分隔（如："a.b.c"）
 * @param value 要设置的值
 */
export function setNestedProperty(obj: any, path: string, value: any) {
  const keys = path.split(".");
  let current = obj;

  // 遍历到倒数第二个键，确保路径存在
  for (let i = 0; i < keys.length - 1; i++) {
    if (!current[keys[i]]) {
      current[keys[i]] = {}; // 创建不存在的路径
    }
    current = current[keys[i]];
  }

  // 设置最终属性值
  current[keys[keys.length - 1]] = value;
}

/**
 * 创建悬停点事件处理函数（旧版本，已被新版本替代）
 * 用于处理3D场景中点对象的鼠标悬停事件
 *
 * @param el 容器HTML元素
 * @param camera 透视相机对象
 * @param viewGroup 包含可交互对象的组
 * @param enterCallback 鼠标进入对象时的回调函数
 * @param leaveCallback 鼠标离开对象时的回调函数
 * @returns 包含事件处理和清理方法的对象
 */
export function createHoverPointEventFun(
  el: HTMLElement,
  camera: PerspectiveCamera,
  viewGroup: Group,
  enterCallback: (data: any) => void,
  leaveCallback: (data: any) => void
) {
  /** 容器宽度 */
  let width: number | null = el.getBoundingClientRect().width;
  /** 容器高度 */
  let height: number | null = el.getBoundingClientRect().height;
  /** 射线投射器，用于检测鼠标位置对应的3D对象 */
  let raycaster: null | Raycaster = new Raycaster();

  /** 当前悬停状态 */
  let status: STATUS = STATUS.leave;
  /** 当前悬停对象的ID */
  let crtObjectId: number | null = null;
  /**
   * 清空闭包造成的内存泄漏
   * 将所有引用设置为null，释放内存
   */
  function clear() {
    width = null;
    height = null;
    raycaster = null;
  }

  /**
   * 鼠标移动事件处理函数
   * 检测鼠标位置对应的3D对象并触发相应的进入/离开回调
   * @param e 鼠标事件对象
   */
  function event(e: MouseEvent) {
    const { offsetX, offsetY } = e;

    // 将屏幕坐标转换为标准化设备坐标
    raycaster!.setFromCamera(
      new Vector2((offsetX / width!) * 2 - 1, -(offsetY / height!) * 2 + 1),
      camera
    );

    // 检测与视图组中所有对象的交集
    const intersects = raycaster!.intersectObjects(
      [...viewGroup?.children!],
      true
    );

    // 如果没有交集且当前状态为进入，触发离开事件
    if (!intersects.length && status === STATUS.enter) {
      leaveCallback(null);
      crtObjectId = null;
      status = STATUS.leave;
      document.body.style.cursor = "";
      return;
    } else if (!intersects.length) return;

    // 距离限制检查
    if (intersects[0].distance > (ctx.maxDistance ?? 70)) return;

    // 查找第一个知识节点
    for (let i = 0; i < intersects.length; i++) {
      if (intersects[i].object.userData.type === GraphType.Point) {
        intersects[0] = intersects[i];
        break;
      }
    }

    // 跳过正在动画的对象
    if (intersects[0].object.userData.isAnimation) return;

    // 处理进入事件
    if (
      intersects[0].object.userData.type === GraphType.Point &&
      crtObjectId != intersects[0].object.id &&
      !intersects[0].object.children.length &&
      (intersects[0].object.parent as any).status !== POINT_STATUS.focus
    ) {
      enterCallback(intersects[0].object.parent);
      document.body.style.cursor = "pointer";
      crtObjectId = intersects[0].object.id;
      status = STATUS.enter;
    } else if (intersects[0].object.id !== crtObjectId) {
      // 处理离开事件
      leaveCallback(intersects[0].object.parent);
      document.body.style.cursor = "";
      crtObjectId = intersects[0].object.id;
      status = STATUS.leave;
    }
  }
  // 返回公开的方法接口
  return {
    clear, // 清理方法
    event, // 事件处理方法
  };
}

/**
 * 获取图中最大的Y值
 * 根据图的最大层级和层级间距计算场景的最大Y坐标
 * @param graph 图数据结构
 * @param levelSpace 层级间距
 * @returns 最大的Y值（绝对值）
 */
export function getMaxY(graph: KsgGraph, levelSpace: number) {
  return Math.abs(graph.getMaxLevel() * levelSpace);
}

/**
 * 获取指定坐标位置的DOM元素
 * @param e 鼠标事件对象
 * @returns 该坐标位置的DOM元素
 */
export function getDomElement(e: MouseEvent) {
  const { clientX, clientY } = e as MouseEvent;
  return document.elementFromPoint(clientX, clientY) as HTMLElement;
}

/**
 * 递归查找有效的父节点
 * 向上遍历DOM树，查找具有css2d-label类名的DIV元素
 * @param ele DOM元素节点
 * @returns 找到的标签元素的ID，如果没找到则返回null
 */
export function findValidateParentNode(ele: Node | null) {
  if (!ele) return null;
  if (ele.nodeName === "DIV" && (ele as Element).className === "css2d-label")
    return (ele as Element).getAttribute("id");
  return findValidateParentNode(ele.parentNode);
}

/**
 * 计算DOM元素的宽高（未挂载到文档时）
 * 临时将元素添加到body中进行尺寸计算，然后移除
 * 适用于计算标签元素在渲染前的尺寸
 * @param el 需要计算尺寸的DOM元素
 * @returns 包含宽度和高度的对象
 */
export function computedWH(el: Element): { width: number; height: number } {
  const size: { width: number; height: number } = {
    width: 0,
    height: 0,
  };

  // 临时添加到body中进行计算
  document.body.appendChild(el);
  size.width = el.getBoundingClientRect().width;
  size.height = el.getBoundingClientRect().height;
  // 计算完成后立即移除
  document.body.removeChild(el);

  return size;
}

/**
 * 计算3D对象在屏幕视口中的像素坐标位置
 * 将3D世界坐标转换为2D屏幕坐标
 * @param object 需要计算位置的3D对象
 * @param camera 透视相机对象
 * @param rendererW 渲染器宽度
 * @param rendererH 渲染器高度
 * @returns 屏幕坐标位置，相对于视口的x,y坐标
 */
export function getObjectPosition(
  object: Object3D,
  camera: PerspectiveCamera,
  rendererW: number,
  rendererH: number
): { x: number; y: number } {
  if (!object) return { x: 0, y: 0 };

  const wordPosition = new Vector3();
  object.getWorldPosition(wordPosition);

  // 转化为NDC（标准化设备坐标）坐标
  // 注意：x∈[-1,1](左到右), y∈[-1,1](下到上)
  wordPosition.project(camera);

  return {
    x: (wordPosition.x * 0.5 + 0.5) * rendererW, // 转换为0-1范围，再乘以渲染器宽度
    y: (0.5 - wordPosition.y * 0.5) * rendererH, // 转换为0-1范围，再乘以渲染器高度
  };
}

/**
 * 根据知识点掌握状态获取对应的Three.js颜色对象
 * @param studyStatus 知识点学习状态枚举值
 * @returns Three.js Color对象
 */
export function studyStatusToColor(studyStatus: POINT_STUDY_STATUS) {
  switch (studyStatus) {
    case POINT_STUDY_STATUS.unstudy:
      return new Color(POINT_STUDY_STATUS_COLOR.unstudy);
    case POINT_STUDY_STATUS.studied:
      return new Color(POINT_STUDY_STATUS_COLOR.studied);
    case POINT_STUDY_STATUS.mastered:
      return new Color(POINT_STUDY_STATUS_COLOR.mastered);
  }
}
