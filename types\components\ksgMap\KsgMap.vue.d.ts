import { type PropType } from "vue";
import { loadMorePointsData } from "./core/loadData";
import { injectMathJax } from "./utils/mathJax";
import { type Options } from "./config/index";
import type { PointData } from "./types";
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    width: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
        required: false;
    };
    height: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
        required: false;
    };
    config: {
        type: PropType<Options>;
        default: () => {};
        required: false;
    };
    loading: {
        type: PropType<"loading" | "loaded" | "error">;
        default: string;
    };
}>, {
    firstLoadPointsData: (pointsData: PointData[], totalPoints: number, rootId?: string) => Promise<void>;
    loadMorePointsData: typeof loadMorePointsData;
    injectMathJax: typeof injectMathJax;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {} & {
    loadMore: (rootId: string, current: number, levelSize: number) => any;
    clickLabel: (id: string) => any;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    width: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
        required: false;
    };
    height: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
        required: false;
    };
    config: {
        type: PropType<Options>;
        default: () => {};
        required: false;
    };
    loading: {
        type: PropType<"loading" | "loaded" | "error">;
        default: string;
    };
}>> & Readonly<{
    onLoadMore?: ((rootId: string, current: number, levelSize: number) => any) | undefined;
    onClickLabel?: ((id: string) => any) | undefined;
}>, {
    width: string | number;
    height: string | number;
    config: Options;
    loading: "error" | "loading" | "loaded";
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
