/**
 * 渲染帧管理器 - KsgMap 3D 场景渲染循环核心模块
 *
 * 该模块负责管理 Three.js 场景的渲染循环，协调各个渲染组件的更新
 * 是 KsgMap 可视化系统的渲染引擎，确保场景的流畅显示和交互响应
 */

import ctx from "../ctx";
import TWEEN from "@tweenjs/tween.js";
import { Clock, Raycaster, Vector2, Vector3 } from "three";
import KsgHover from "../core/KsgHover";
import focusCrust from "../core/focusCrust";
import { focusLabel, hoverLabel } from "../core/KsgLabel";

// 调试计数器，用于减少日志输出频率
let debugCounter = 0;
// 当前显示的节点，用于防抖
let currentDisplayedPoint: any = null;
// 节点稳定计数器，用于确保节点在屏幕中心停留足够时间
let stableCounter = 0;
// 当前显示标签的节点
let currentLabelPoint: any = null;
// 标签显示计数器，确保标签显示足够长时间
let displayCounter = 0;
// 需要稳定的帧数（约0.2秒，降低要求）
const STABLE_FRAMES_REQUIRED = 12;
// 屏幕中心区域的阈值（距离中心的像素距离）
const CENTER_THRESHOLD = 400;
// 最小显示时间（帧数），确保标签显示足够长时间（3秒）
const MIN_DISPLAY_FRAMES = 180;

/**
 * 更新最靠近相机的节点标签显示
 * 使用改进的3D空间算法，基于相机朝向而非屏幕坐标
 */
function updateClosestPointLabel() {
  debugCounter++;
  // 检查必要的对象是否存在
  if (!ctx.pointsMesh || !ctx.camera || !ctx.viewRange) {
    return;
  }

  let bestCandidate: any = null;
  let totalPoints = 0;
  let visiblePoints = 0;
  let skippedPoints = 0;
  let allScores: number[] = [];

  // 获取相机的朝向向量（相机正前方）
  const cameraDirection = new Vector3();
  ctx.camera.getWorldDirection(cameraDirection);

  // 获取相机位置
  const cameraPosition = ctx.camera.position.clone();

  // 遍历所有子节点，使用3D空间算法找到最适合显示标签的节点
  ctx.pointsMesh.pointsData.forEach((point: any) => {
    totalPoints++;
    // 跳过当前聚焦的父节点
    if (point.index === ctx.pointsMesh?.focusIndex) {
      skippedPoints++;
      return;
    }

    // 只考虑当前聚焦父节点的子节点
    if (!ctx.focusChildrenIndex?.has(point.index)) {
      skippedPoints++;
      return;
    }

    try {
      // 获取节点的3D世界坐标
      const nodeWorldPos = new Vector3(...point.coordinate);
      ctx.pointsMesh!.localToWorld(nodeWorldPos);

      // 计算从相机到节点的向量
      const cameraToNode = nodeWorldPos.clone().sub(cameraPosition);

      // 计算节点在相机朝向上的投影（正值表示在相机前方）
      const forwardProjection = cameraToNode.dot(cameraDirection);

      // 只考虑在相机前方的节点
      if (forwardProjection > 0) {
        // 计算节点到相机朝向轴的距离（越小越靠近中心）
        const lateralDistance = cameraToNode.clone()
          .sub(cameraDirection.clone().multiplyScalar(forwardProjection))
          .length();

        // 计算综合评分：距离相机越近，偏离中心轴越小，评分越高
        const distanceScore = 1 / (1 + cameraToNode.length() * 0.1);
        const centerScore = 1 / (1 + lateralDistance * 2);
        const totalScore = distanceScore * centerScore;

        allScores.push(totalScore);

        // 获取屏幕坐标用于后续显示
        const screenPos = ctx.pointsMesh!.getWorldP(
          point.index,
          ctx.camera!,
          ctx.viewRange!.maxX - ctx.viewRange!.minX,
          ctx.viewRange!.maxY - ctx.viewRange!.minY
        );

        // 检查是否在可视范围内
        if (screenPos.x >= ctx.viewRange!.minX &&
            screenPos.x <= ctx.viewRange!.maxX &&
            screenPos.y >= ctx.viewRange!.minY &&
            screenPos.y <= ctx.viewRange!.maxY) {

          visiblePoints++;

          // 更新最佳候选节点
          if (!bestCandidate || totalScore > bestCandidate.score) {
            bestCandidate = {
              point,
              score: totalScore,
              screenPos,
              lateralDistance,
              forwardProjection
            };
          }
        }
      }
    } catch (error) {
      // 忽略处理失败的节点
    }
  });

  // 每60帧输出一次调试信息（约1秒一次）
  if (debugCounter % 60 === 0) {
    const minScore = allScores.length > 0 ? Math.min(...allScores) : 0;
    const maxScore = allScores.length > 0 ? Math.max(...allScores) : 0;
    const childrenCount = ctx.focusChildrenIndex?.size || 0;
    console.log('[RotateLabel] 3D Algorithm Stats:', {
      total: totalPoints,
      skipped: skippedPoints,
      visible: visiblePoints,
      childrenTotal: childrenCount,
      bestCandidateFound: !!bestCandidate,
      bestCandidateName: bestCandidate?.point?.name,
      bestScore: bestCandidate?.score?.toFixed(4),
      lateralDistance: bestCandidate?.lateralDistance?.toFixed(2),
      forwardProjection: bestCandidate?.forwardProjection?.toFixed(2),
      minScore: minScore.toFixed(4),
      maxScore: maxScore.toFixed(4)
    });
  }

  // 智能标签显示逻辑（使用3D算法结果）
  if (bestCandidate) {
    const candidatePoint = bestCandidate.point;

    // 检查是否是同一个节点
    if (currentDisplayedPoint && currentDisplayedPoint.index === candidatePoint.index) {
      // 同一个节点，增加稳定计数
      stableCounter++;
    } else {
      // 不同节点，重置计数器
      currentDisplayedPoint = candidatePoint;
      stableCounter = 1;
    }

    // 更宽松的显示条件：稳定时间更短
    if (stableCounter >= STABLE_FRAMES_REQUIRED) {
      // 只有当找到的节点不是当前聚焦节点时，才使用hoverLabel显示
      if (candidatePoint.index !== ctx.pointsMesh?.focusIndex) {
        // 显示子节点标签
        if (hoverLabel.lastIndex !== candidatePoint.index) {
          try {
            console.log('[RotateLabel] Displaying child node label for:', candidatePoint.name,
                       'score:', bestCandidate.score.toFixed(4), 'stable frames:', stableCounter);
            hoverLabel.display(candidatePoint, {
              viewRange: ctx.viewRange!,
              dnc: bestCandidate.screenPos
            });
            currentLabelPoint = candidatePoint;
            displayCounter = 0;
          } catch (error) {
            console.log('[RotateLabel] Error displaying child label:', error);
          }
        }

        // 如果标签已经显示，增加显示计数
        if (currentLabelPoint && currentLabelPoint.index === candidatePoint.index) {
          displayCounter++;
        }
      } else {
        // 如果最近的节点就是聚焦节点，隐藏hoverLabel
        if (hoverLabel.visible) {
          hoverLabel.hide();
          console.log('[RotateLabel] Best candidate is focus point, hiding hover label');
        }
        currentLabelPoint = null;
        displayCounter = 0;
      }
    }
  } else {
    // 没有找到合适的候选节点
    currentDisplayedPoint = null;
    stableCounter = 0;

    // 如果当前有标签显示，检查是否应该继续显示一段时间
    if (currentLabelPoint && displayCounter < MIN_DISPLAY_FRAMES) {
      displayCounter++;
      console.log('[RotateLabel] Keeping child label visible for:', currentLabelPoint.name, 'frames left:', MIN_DISPLAY_FRAMES - displayCounter);
    } else {
      // 隐藏标签
      if (hoverLabel.visible) {
        hoverLabel.hide();
        console.log('[RotateLabel] No suitable candidate found, hiding hover label');
      }
      currentLabelPoint = null;
      displayCounter = 0;
    }
  }
}

/**
 * 渲染帧 Hook - 管理 3D 场景的渲染循环和动画更新
 *
 * 功能特性：
 * - 统一管理场景渲染循环
 * - 协调多个渲染器的更新（WebGL、CSS2D）
 * - 处理动画系统更新（Tween、节点动画、焦点动画）
 * - 管理相机控制器更新
 * - 提供稳定的 60FPS 渲染性能
 *
 * @returns 包含渲染循环启动方法的对象
 */
export default function useRenderFrame() {
  // Three.js 时钟对象 - 用于计算每帧的时间间隔，确保动画的时间一致性
  const clock = new Clock();

  /**
   * 启动渲染循环 - 核心渲染函数，每帧调用一次
   *
   * 渲染管道执行顺序：
   * 1. 计算帧时间间隔
   * 2. CSS2D 标签渲染（知识点标签）
   * 3. WebGL 主场景渲染
   * 4. 相机控制器更新
   * 5. 动画系统更新
   * 6. 自定义组件更新
   * 7. 递归调用下一帧
   *
   * @param time - 当前时间戳，由 requestAnimationFrame 提供
   */
  function startRenderFrame(time: any = 0) {
    // 计算自上一帧以来的时间间隔（秒）
    const deltaTime = clock.getDelta();

    // 注释：视椎体裁剪更新（可能用于性能优化）
    // updateVisible(ctx.viewGroup!, ctx.camera!);

    // CSS2D 渲染器 - 渲染知识点标签等 2D 元素
    ctx.css2dRenderer?.render(ctx.scene!, ctx.camera!);

    // 注释：后处理渲染管道（可能用于特效处理）
    // ctx.composer?.render();

    // 主 WebGL 渲染器 - 渲染 3D 场景
    ctx.renderer?.render(ctx.scene!, ctx.camera!);

    // 相机控制器更新 - 处理用户交互（旋转、缩放、平移）
    ctx.controls?.update(deltaTime);
    // 自动旋转更新（如果启用）
    ctx.controls?.autoRotateUpdate(deltaTime);

    // 在自动旋转时显示最近节点的标签
    if (ctx.controls?.autoRotate) {
      // console.log('[RotateLabel] Auto rotate is active, updating closest point label');
      updateClosestPointLabel();
    }

    // Tween.js 动画系统更新 - 处理补间动画
    TWEEN.update(time);

    // 递归调用，实现连续渲染循环
    requestAnimationFrame(startRenderFrame);

    // 自定义组件更新
    // 节点悬停动画更新
    KsgHover.update(ctx, deltaTime);
    // 焦点外壳动画更新
    focusCrust.update(deltaTime);
    // 点云网格更新（如果存在）
    if (ctx.pointsMesh) ctx.pointsMesh.update();
    // 焦点连线更新（如果存在）
    if (ctx.focusLine) ctx.focusLine?.update();
  }

  // 返回渲染控制接口
  return {
    startRenderFrame, // 启动渲染循环的方法
  };
}
