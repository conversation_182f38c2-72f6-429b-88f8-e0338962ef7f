/**
 * 渲染帧管理器 - KsgMap 3D 场景渲染循环核心模块
 *
 * 该模块负责管理 Three.js 场景的渲染循环，协调各个渲染组件的更新
 * 是 KsgMap 可视化系统的渲染引擎，确保场景的流畅显示和交互响应
 */

import ctx from "../ctx";
import TWEEN from "@tweenjs/tween.js";
import { Clock, Raycaster, Vector2 } from "three";
import KsgHover from "../core/KsgHover";
import focusCrust from "../core/focusCrust";
import { focusLabel, hoverLabel } from "../core/KsgLabel";

// 调试计数器，用于减少日志输出频率
let debugCounter = 0;
// 当前显示的节点，用于防抖
let currentDisplayedPoint: any = null;
// 节点稳定计数器，用于确保节点在屏幕中心停留足够时间
let stableCounter = 0;
// 需要稳定的帧数（约0.5秒）
const STABLE_FRAMES_REQUIRED = 30;
// 屏幕中心区域的阈值（距离中心的像素距离）
const CENTER_THRESHOLD = 300;

/**
 * 更新最靠近相机的节点标签显示
 * 在自动旋转时调用，找到距离屏幕中心最近且稳定的节点
 */
function updateClosestPointLabel() {
  debugCounter++;
  // 检查必要的对象是否存在
  if (!ctx.pointsMesh || !ctx.camera || !ctx.viewRange) {
    return;
  }

  let centerPoint: any = null;
  let totalPoints = 0;
  let visiblePoints = 0;
  let skippedPoints = 0;
  let allDistances: number[] = [];

  // 计算屏幕中心坐标
  const centerX = (ctx.viewRange!.minX + ctx.viewRange!.maxX) / 2;
  const centerY = (ctx.viewRange!.minY + ctx.viewRange!.maxY) / 2;

  // 遍历所有节点，找到最靠近屏幕中心的节点
  ctx.pointsMesh.pointsData.forEach((point: any) => {
    totalPoints++;
    // 只跳过当前聚焦的节点
    if (point.index === ctx.pointsMesh?.focusIndex) {
      skippedPoints++;
      return;
    }

    // 获取节点的屏幕坐标
    try {
      const screenPos = ctx.pointsMesh!.getWorldP(
        point.index,
        ctx.camera!,
        ctx.viewRange!.maxX - ctx.viewRange!.minX,
        ctx.viewRange!.maxY - ctx.viewRange!.minY
      );

      // 检查节点是否在可视范围内
      if (screenPos.x >= ctx.viewRange!.minX &&
          screenPos.x <= ctx.viewRange!.maxX &&
          screenPos.y >= ctx.viewRange!.minY &&
          screenPos.y <= ctx.viewRange!.maxY) {

        visiblePoints++;

        // 计算节点到屏幕中心的距离
        const distanceToCenter = Math.sqrt(
          Math.pow(screenPos.x - centerX, 2) +
          Math.pow(screenPos.y - centerY, 2)
        );

        // 收集所有距离用于调试
        allDistances.push(distanceToCenter);

        // 只考虑在中心区域附近的节点（距离中心小于阈值）
        if (distanceToCenter <= CENTER_THRESHOLD) {
          // 如果这是第一个中心区域的节点，或者比当前中心节点更近
          if (!centerPoint || distanceToCenter <
              Math.sqrt(Math.pow(centerPoint.screenPos.x - centerX, 2) +
                       Math.pow(centerPoint.screenPos.y - centerY, 2))) {
            centerPoint = {
              point,
              distance: distanceToCenter,
              screenPos
            };
          }
        }
      }
    } catch (error) {
      // 忽略获取屏幕坐标失败的节点
    }
  });

  // 每60帧输出一次调试信息（约1秒一次）
  if (debugCounter % 60 === 0) {
    const minDistance = allDistances.length > 0 ? Math.min(...allDistances) : 0;
    const maxDistance = allDistances.length > 0 ? Math.max(...allDistances) : 0;
    console.log('[RotateLabel] Stats:', {
      total: totalPoints,
      skipped: skippedPoints,
      visible: visiblePoints,
      centerFound: !!centerPoint,
      centerName: centerPoint?.point?.name,
      centerDistance: centerPoint?.distance?.toFixed(2),
      threshold: CENTER_THRESHOLD,
      minDistanceToCenter: minDistance.toFixed(2),
      maxDistanceToCenter: maxDistance.toFixed(2),
      allDistances: allDistances.map(d => d.toFixed(0)).join(', ')
    });
  }

  // 防抖逻辑：确保节点在中心区域稳定停留
  if (centerPoint) {
    const candidatePoint = centerPoint.point;

    // 检查是否是同一个节点
    if (currentDisplayedPoint && currentDisplayedPoint.index === candidatePoint.index) {
      // 同一个节点，增加稳定计数
      stableCounter++;
    } else {
      // 不同节点，重置计数器
      currentDisplayedPoint = candidatePoint;
      stableCounter = 1;
    }

    // 只有当节点稳定停留足够时间后才显示标签
    if (stableCounter >= STABLE_FRAMES_REQUIRED) {
      // 只有当找到的节点不是当前聚焦节点时，才使用hoverLabel显示
      if (candidatePoint.index !== ctx.pointsMesh?.focusIndex) {
        // 显示标签（如果不是当前已显示的节点）
        if (hoverLabel.lastIndex !== candidatePoint.index) {
          try {
            console.log('[RotateLabel] Displaying stable hover label for:', candidatePoint.name,
                       'distance:', centerPoint.distance.toFixed(2), 'stable frames:', stableCounter);
            hoverLabel.display(candidatePoint, {
              viewRange: ctx.viewRange!,
              dnc: centerPoint.screenPos
            });
          } catch (error) {
            console.log('[RotateLabel] Error displaying label:', error);
          }
        }
      } else {
        // 如果最近的节点就是聚焦节点，隐藏hoverLabel
        if (hoverLabel.visible) {
          hoverLabel.hide();
          console.log('[RotateLabel] Center point is focus point, hiding hover label');
        }
      }
    }
  } else {
    // 没有找到中心区域的节点，重置状态并隐藏标签
    currentDisplayedPoint = null;
    stableCounter = 0;

    if (hoverLabel.visible) {
      hoverLabel.hide();
      console.log('[RotateLabel] No center point found, hiding hover label');
    }
  }
}

/**
 * 渲染帧 Hook - 管理 3D 场景的渲染循环和动画更新
 *
 * 功能特性：
 * - 统一管理场景渲染循环
 * - 协调多个渲染器的更新（WebGL、CSS2D）
 * - 处理动画系统更新（Tween、节点动画、焦点动画）
 * - 管理相机控制器更新
 * - 提供稳定的 60FPS 渲染性能
 *
 * @returns 包含渲染循环启动方法的对象
 */
export default function useRenderFrame() {
  // Three.js 时钟对象 - 用于计算每帧的时间间隔，确保动画的时间一致性
  const clock = new Clock();

  /**
   * 启动渲染循环 - 核心渲染函数，每帧调用一次
   *
   * 渲染管道执行顺序：
   * 1. 计算帧时间间隔
   * 2. CSS2D 标签渲染（知识点标签）
   * 3. WebGL 主场景渲染
   * 4. 相机控制器更新
   * 5. 动画系统更新
   * 6. 自定义组件更新
   * 7. 递归调用下一帧
   *
   * @param time - 当前时间戳，由 requestAnimationFrame 提供
   */
  function startRenderFrame(time: any = 0) {
    // 计算自上一帧以来的时间间隔（秒）
    const deltaTime = clock.getDelta();

    // 注释：视椎体裁剪更新（可能用于性能优化）
    // updateVisible(ctx.viewGroup!, ctx.camera!);

    // CSS2D 渲染器 - 渲染知识点标签等 2D 元素
    ctx.css2dRenderer?.render(ctx.scene!, ctx.camera!);

    // 注释：后处理渲染管道（可能用于特效处理）
    // ctx.composer?.render();

    // 主 WebGL 渲染器 - 渲染 3D 场景
    ctx.renderer?.render(ctx.scene!, ctx.camera!);

    // 相机控制器更新 - 处理用户交互（旋转、缩放、平移）
    ctx.controls?.update(deltaTime);
    // 自动旋转更新（如果启用）
    ctx.controls?.autoRotateUpdate(deltaTime);

    // 在自动旋转时显示最近节点的标签
    if (ctx.controls?.autoRotate) {
      // console.log('[RotateLabel] Auto rotate is active, updating closest point label');
      updateClosestPointLabel();
    }

    // Tween.js 动画系统更新 - 处理补间动画
    TWEEN.update(time);

    // 递归调用，实现连续渲染循环
    requestAnimationFrame(startRenderFrame);

    // 自定义组件更新
    // 节点悬停动画更新
    KsgHover.update(ctx, deltaTime);
    // 焦点外壳动画更新
    focusCrust.update(deltaTime);
    // 点云网格更新（如果存在）
    if (ctx.pointsMesh) ctx.pointsMesh.update();
    // 焦点连线更新（如果存在）
    if (ctx.focusLine) ctx.focusLine?.update();
  }

  // 返回渲染控制接口
  return {
    startRenderFrame, // 启动渲染循环的方法
  };
}
