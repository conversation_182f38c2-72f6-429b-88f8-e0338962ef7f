import type { KsgControls } from './KsgControls';

/**
 * 自动旋转管理器配置选项
 */
export interface AutoRotateManagerOptions {
  /** 用户停止操作后多少毫秒重新启动自动旋转，默认5000ms */
  restartDelay?: number;
  /** 是否在初始化时立即启动自动旋转，默认true */
  autoStart?: boolean;
  /** 自动旋转速度，默认2.0 */
  rotateSpeed?: number;
  /** 是否启用调试日志，默认false */
  debug?: boolean;
}

/**
 * 自动旋转管理器类
 * 负责管理KsgControls的自动旋转功能
 */
export declare class AutoRotateManager {
  /**
   * 构造函数
   * @param controls KsgControls实例
   * @param options 配置选项
   */
  constructor(controls: KsgControls, options?: AutoRotateManagerOptions);

  /**
   * 启动自动旋转
   * 立即启用自动旋转并开始监听用户交互
   */
  start(): void;

  /**
   * 停止自动旋转
   * 立即停止自动旋转并清除所有定时器
   */
  stop(): void;

  /**
   * 暂停自动旋转并启动重启定时器
   * 用户交互时调用，会在指定延迟后自动重启
   */
  pause(): void;

  /**
   * 重置重启定时器
   * 用户持续交互时调用，重新计算延迟时间
   */
  resetTimer(): void;

  /**
   * 销毁管理器
   * 清理所有资源和事件监听器
   */
  destroy(): void;

  /**
   * 获取当前自动旋转状态
   */
  get isActive(): boolean;

  /**
   * 设置自动旋转速度
   */
  setSpeed(speed: number): void;
}
