declare module "three/examples/jsm/renderers/CSS2DRenderer.js" {
  import { Object3D } from "three";
  export class CSS2DObject extends Object3D {
    element: HTMLElement;
    center: {
      set(x: number, y: number): void;
    };
    constructor(element: HTMLElement);
  }
}

declare module "three/addons/renderers/CSS2DRenderer.js" {
  export const CSS2DRenderer: any;
}

declare module "three/webgpu" {
  export const WebGPURenderer: any;
  export const Line2NodeMaterial: any;
  export const NodeMaterial: any;
  export const NodeMaterialParameters: any;
  export const Camera: any;
  export const DirectionalLightShadow: any;
  export const Light: any;
  export const Object3D: any;
  export const ShadowBaseNode: any;
  // 视情况声明更多需要的 mock 类型
}


//声明vue组件类型
declare module '*.vue' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}