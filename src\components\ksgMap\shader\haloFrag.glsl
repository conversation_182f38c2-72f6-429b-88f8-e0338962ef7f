uniform float uTime;
//uniform vec2 uResolution;
uniform vec2 uSpriteSize;
uniform float uSpeed;
uniform float uMinRadius;
uniform int uNumRings; // 圆环数量
uniform float uRingDuration; // 单个圆环的持续时间
uniform vec3 uColor;
varying vec2 vUv;

uniform float uDelay; // 圆环之间的延迟时间

void main() {
    vec2 uv = vUv - 0.5;
    float maxRadius = min(uSpriteSize.x, uSpriteSize.y) * 0.5;
    float distFromCenter = length(uv) * min(uSpriteSize.x, uSpriteSize.y);

    vec4 ringColor = vec4(uColor,0.7); // 统一的颜色
    vec4 totalColor = vec4(0.0);

    float currentTime = uTime * uSpeed;

    // 计算总持续时间和当前周期内的相对时间
    float totalDuration = uRingDuration + float(uNumRings - 1) * uDelay;
    float cycleTime = mod(currentTime, totalDuration); // 在总持续时间内循环

    for (int i = 0; i < uNumRings; ++i) {
        // 计算每个圆环的开始时间和结束时间，加入延迟
        float ringStart = float(i) * uDelay; // 每个光环之间增加 uDelay 的延迟
        float ringEnd = ringStart + uRingDuration;

        // 如果当前时间落在这个圆环的时间窗口内，则绘制该圆环
        if (cycleTime >= ringStart && cycleTime < ringEnd) {
            float ringTimer = clamp(cycleTime - ringStart, 0.0, uRingDuration);

            // 根据当前计时计算圆环的半径
            float ringRadius = mix(uMinRadius / min(uSpriteSize.x, uSpriteSize.y), maxRadius, ringTimer / uRingDuration);

            float innerEdge = ringRadius - 0.01;
            float outerEdge = ringRadius + 0.01;

            // 使用 smoothstep 创建平滑过渡效果
            float ring = smoothstep(outerEdge, innerEdge, distFromCenter);

            // 应用衰减因子，使圆环随时间逐渐消失
            float attenuation = exp(-pow(distFromCenter - ringRadius, 2.0) * 100.0) * (1.0 - ringTimer / uRingDuration);

            // 使用统一的颜色
            vec4 currentRingColor = ringColor * ring * attenuation;

            // 累加每个圆环的颜色
            totalColor += currentRingColor;
        }
    }

    // 确保颜色值不超过1
    gl_FragColor = clamp(totalColor, 0.0, 0.5);
}