import ctx from "../ctx";
import type { PointData } from "../types";
import {
  firstRenderSignalRootPoints,
  firstRenderMultiplyRootPoints,
  renderMoreData,
} from "./renderData";
import { getMaxY } from "../utils";
import { MODE } from "../enums";
import KsgGraph, { frameScheduler } from "./KsgGraph";

/**
 * 数据加载器模块 - loadData.ts
 * 
 * 职责：
 * 1. 处理初始节点数据的加载和渲染
 * 2. 管理增量数据的动态加载
 * 3. 协调图计算与渲染的执行流程
 * 4. 根据不同模式选择相应的渲染策略
 * 
 * 核心流程：
 * pointsData → KsgGraph计算 → frameScheduler异步执行 → 渲染模式分发
 */

/**
 * 加载知识点数据 - 系统初始化的入口函数
 * 
 * 执行流程：
 * 1. 创建KsgGraph实例，开始图布局计算
 * 2. 等待frameScheduler异步计算完成
 * 3. 更新相机控制器的Y轴范围限制
 * 4. 根据模式选择渲染策略（单根/多根）
 * 
 * @param pointsData 原始节点数据数组 - 包含节点关系和属性信息
 * @param totalPoints 节点总数量 - 用于内存预分配和分页控制
 * @param rootId 根节点ID - 单根模式下的聚焦起点
 */
export async function loadPointsData(
  pointsData: PointData[],
  totalPoints: number,
  rootId?: string
) {
  // 步骤1：创建图计算实例，启动布局算法
  // KsgGraph会自动开始计算节点层级和3D坐标
  ctx.graph = new KsgGraph(pointsData);
  
  // 步骤2：监听异步计算完成事件
  // frameScheduler确保计算不阻塞UI线程
  frameScheduler.onCompleted(() => {
    // 步骤3：计算图的最大Y轴深度，用于相机控制
    ctx.maxLevelY = getMaxY(ctx.graph!, ctx.levelSpace as number);
    
    // 步骤4：更新相机控制器的Y轴移动范围
    // 防止用户查看超出图形边界的区域
    ctx.controls!.yMinRange = -ctx.maxLevelY - 20;
    
    // 步骤5：设置分页器的总数和当前页大小
    ctx.pointsLevelPager!.total = totalPoints;
    ctx.pointsLevelPager!.current += ctx.pointsLevelPager!.levelSize;
    
    // 步骤6：设置初始聚焦节点ID
    ctx.focusPointInfo!.pointId = rootId || "";
    
    // 步骤7：根据渲染模式选择对应的渲染策略
    switch (ctx.model) {
      case MODE.Single_ROOT:
        // 单根模式：有明确的起始聚焦点，适用于层次化知识结构
        firstRenderSignalRootPoints();
        break;
      case MODE.MULTIPLE_ROOT:
        // 多根模式：没有固定聚焦点，适用于并列的知识领域
        firstRenderMultiplyRootPoints();
        break;
    }
  });
}

/**
 * 加载更多数据 - 增量数据加载函数
 * 
 * 用于懒加载场景，当用户需要查看更多节点时调用
 * 支持增量更新，避免重新计算整个图结构
 * 
 * 执行流程：
 * 1. 调用graph.loadMore()进行增量计算
 * 2. 获取差异数据（新增节点 + 位置变化节点）
 * 3. 更新相机控制范围
 * 4. 触发增量渲染动画
 * 
 * @param pointsData 新增的节点数据数组
 */
export function loadMorePointsData(pointsData: PointData[]) {
  // 执行增量图计算，返回差异数据
  ctx.graph!.loadMore(pointsData).then((diffData) => {
    // 重新计算图的最大深度
    ctx.maxLevelY = getMaxY(ctx.graph!, ctx.levelSpace as number);
    
    // 更新控制器的Y轴下边界，留出缓冲空间
    ctx.controls!.yMinRange = -ctx.maxLevelY - 6;
    
    // 获取当前聚焦节点的索引，用于后续连线动画
    const focusIndex = ctx.pointsMesh!.focusIndex;
    
    // 执行增量渲染，包含节点出现动画和连线动画
    renderMoreData(focusIndex, diffData);
  });
}
