/**
 * 该文件负责管理 KsgMap 组件中 Three.js 相关的所有默认配置项，
 * 包括相机、渲染器、场景、控制器等核心配置以及知识图谱特有的配置项。
 */

import type {
  CameraConfig,
  RendererConfig,
  SceneConfig,
  ControlsConfig,
} from "../types/index";
import ctx from "../ctx";
import { MODE } from "../enums";
import { MOUSE } from "three";

/**
 * KsgMap 默认配置对象
 * 包含所有组件初始化所需的默认参数
 */
const defaultConfig: any = {
  /** 模式配置：单根节点模式 */
  model: MODE.Single_ROOT,

  /** 视口显示范围配置 */
  viewRange: {
    minX: 0,
    maxX: 0,
    minY: 0,
    maxY: 0,
  },

  /** 相机配置参数 */
  camera: {
    /** 视野角度（度） */
    fov: 45,
    // aspect: 1.778, //在初始化的时候需要根据画布尺寸配置，默认900：506
    /** 宽高比，根据画布尺寸动态计算，默认1200x675 */
    aspect: 1200 / 675,
    /** 近裁剪面距离 */
    near: 0.1,
    /** 远裁剪面距离 */
    far: 1000,
    /** 相机初始位置 */
    position: {
      x: 30.2,
      y: -3.14,
      z: 24.98,
    },
  },

  /** 渲染器配置参数 */
  renderer: {
    /** 画布宽度 */
    width: 1200,
    /** 画布高度 */
    height: 675,
    /** WebGL渲染器特定配置 */
    webGLRenderer: {
      /** 是否启用抗锯齿 */
      antialias: true,
    },
  },

  /** 场景配置参数 */
  scene: {
    /** 背景强度 */
    backgroundIntensity: 0.02,
    /** 场景模糊度 */
    backgroundBlurriness: 0.0,
    /** 溯源图整体位置偏移 [x, y, z] */
    groupPosition: [0, 6, 0],
  },

  /** 控制器配置参数 */
  controls: {
    /** 控制器初始位置 [x, y, z] */
    position: [30, -3.14, 24],
    /** 控制器目标点 [x, y, z] */
    target: [0, 0, 0],
    /** 最小极角（弧度），限制垂直旋转下限 */
    minPolarAngle: 0.78539,
    /** 最大极角（弧度），限制垂直旋转上限 */
    maxPolarAngle: 2.35619,
    /** 最小缩放距离 */
    minDistance: 1,
    /** 最大缩放距离 */
    maxDistance: 5000,
    /** 鼠标按键映射配置 */
    mouseButtons: {
      /** 左键：旋转 */
      LEFT: MOUSE.ROTATE,
      /** 中键：缩放 */
      MIDDLE: MOUSE.DOLLY,
      /** 右键：平移 */
      RIGHT: MOUSE.PAN,
    },
    /** 是否启用阻尼效果 */
    enableDamping: true,
    /** Y轴移动范围下限 */
    yMinRange: -1000,
    /** Y轴移动范围上限 */
    yMaxRange: 110,
    /** Y轴移动增量 */
    yDelta: 100,
  },

  /** 知识节点分页配置 */
  pointsLevelPager: {
    /** 当前显示层级 */
    current: 1,
    /** 总层级数量 */
    levelSize: 4,
    /** 节点总数（无限大表示不限制） */
    total: Infinity,
  },

  /** 层级间距离 */
  levelSpace: 15,
  /** 节点间距离 */
  pointSpace: 7,

  /** 知识节点显示配置 */
  point: {
    /** 节点半径 */
    radius: 0.5,
    /** 节点间距 */
    space: 2,
  },

  /** 连线流光效果相关配置 */
  line: {
    /** 流光长度 */
    length: 0.4,
    /** 流光移动速度 */
    speed: 0.15,
    /** 多条连线流光效果是否随机播放 */
    isRandom: false,
  },

  /** 悬停标签偏移配置 */
  hoverLabel: {
    /** X轴偏移量（像素） */
    offsetX: 15,
    /** Y轴偏移量（像素） */
    offsetY: 15,
  },

  /** 弹窗显示距离限制 */
  maxDistance: 110,

  /** 回退到父级节点的回调函数 */
  focusBack: () => {},
  /** 回退到根节点的回调函数 */
  focusBackToRoot: () => {},
};
/**
 * 初始化 Three.js 配置的 Hook 函数
 *
 * 该函数接收用户自定义配置选项，与默认配置进行合并，
 * 并更新全局上下文，最后返回格式化后的配置对象。
 *
 * @param option - 用户自定义配置选项
 * @returns 包含各模块配置的对象
 *
 * @example
 * ```typescript
 * const config = useInitThreeJsConfig({
 *   camera: { fov: 60 },
 *   renderer: { width: 800, height: 600 }
 * });
 * ```
 */
export function useInitThreeJsConfig(option: Options = {}) {
  // 遍历用户配置的每个属性
  for (const key of Object.keys(option) as Array<keyof Options>) {
    const value = option[key];

    // 如果是对象类型且不为null，进行深度合并
    if (typeof value === "object" && value !== null) {
      // 合并对象属性配置
      Object.assign(defaultConfig[key], value);
    } else {
      // 直接替换基础类型值
      (defaultConfig[key] as any) = value;
    }
  }

  // 将最终配置同步到全局上下文
  Object.assign(ctx, {
    ...defaultConfig,
  });

  // 返回格式化的配置对象，便于各模块使用
  return {
    /** 相机配置 */
    cameraConfig: defaultConfig.camera,
    /** 渲染器配置 */
    renderConfig: defaultConfig.renderer,
    /** 场景配置 */
    sceneConfig: defaultConfig.scene,
    /** 控制器配置 */
    controlsConfig: defaultConfig.controls,
    /** 包装容器尺寸配置 */
    wrapperEleSizeConfig: {
      width: defaultConfig.renderer.width as number,
      height: defaultConfig.renderer.height as number,
    },
  };
}

/**
 * KsgMap 配置选项类型定义
 *
 * 定义了所有可配置的选项类型，用户可以通过传入这些选项来自定义组件行为。
 * 所有属性都是可选的，未配置的属性将使用默认值。
 */
export type Options = {
  /** 显示模式配置 */
  model?: MODE;

  /** 视口范围配置 */
  viewRange?: {
    /** 最小X坐标 */
    minX: number;
    /** 最大X坐标 */
    maxX: number;
    /** 最小Y坐标 */
    minY: number;
    /** 最大Y坐标 */
    maxY: number;
  };

  /** 相机配置参数 */
  camera?: CameraConfig;

  /** 渲染器配置参数 */
  renderer?: RendererConfig;

  /** 场景配置参数 */
  scene?: SceneConfig;

  /** 控制器配置参数 */
  controls?: ControlsConfig;

  /** 知识节点分页查询配置 */
  pointsLevelPager?: {
    /** 当前显示层级 */
    current: number;
    /** 总层级数量 */
    levelSize: number;
    /** 节点总数（可选） */
    total?: number;
  };

  /** 层级间隔距离 */
  levelSpace?: number;

  /** 知识节点相关配置 */
  point?: {
    /** 节点半径大小 */
    radius: number;
    /** 节点之间的间距 */
    space: number;
  };

  /** 连线流光效果相关配置 */
  line?: {
    /** 流光长度 */
    length?: number;
    /** 流光移动速度 */
    speed?: number;
    /** 多条连线流光效果是否随机播放 */
    isRandom?: boolean;
  };

  /** 悬停弹窗位置偏移配置 */
  hoverLabel?: {
    /** X轴偏移量（像素） */
    offsetX: number;
    /** Y轴偏移量（像素） */
    offsetY: number;
  };

  /** 弹窗显示距离限制 */
  maxDistance?: number;

  /** 节点之间的间距 */
  pointSpace?: number;

  /** 回退到根节点的回调函数 */
  focusBackToRoot?: () => void;

  /** 回退到父级节点的回调函数 */
  focusBack?: () => void;
};
