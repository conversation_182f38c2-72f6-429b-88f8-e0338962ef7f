import { LOAD_STATUS } from "../enums";
import type { EventsCallback, Size } from "../types";
/**
 * 初始化事件
 */
export default function useInitEvents(wrapperElSize: Size, events: EventsCallback): {
    initEvents: (containerEle: HTMLElement) => void;
    destroyEvents: () => void;
    focusBackToRoot: () => void;
    focusBack: () => void;
    updateClickEventSize: (w: number, h: number) => void;
    updateHoverEventSize: (w: number, h: number) => void;
    changeLoadStatus: (status: LOAD_STATUS) => void;
};
