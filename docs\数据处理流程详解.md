# KsgMap 知识图谱数据处理流程详解

## 概述

KsgMap 采用分层架构处理知识图谱数据，从原始数据到最终3D渲染经历了多个阶段的处理和优化。整个流程注重性能、可扩展性和用户体验。

## 总体架构

```
原始数据 → 图计算 → 布局算法 → 渲染管线 → 用户交互
   ↓         ↓        ↓         ↓         ↓
PointData  KsgGraph  坐标计算   GPU渲染   事件响应
```

## 1. 数据输入阶段

### 1.1 原始数据结构 (PointData)

```typescript
interface PointData {
  pointId: string;           // 节点唯一标识
  pointName: string;         // 节点显示名称
  parentPointIds: string[];  // 父节点ID数组
  isMilestone: boolean;      // 是否为里程碑节点
  status: StudyStatus;       // 学习状态
}
```

### 1.2 数据验证和预处理

- **关系验证**：检查父子关系的完整性
- **环检测**：确保图结构为DAG（有向无环图）
- **数据清洗**：移除无效节点和重复关系

## 2. 图计算阶段 (KsgGraph)

### 2.1 图构建过程

```typescript
// 核心处理流程
constructor(pointsData: PointData[]) {
  this.compute(pointsData);
}

compute(pointsData: PointData[]) {
  this.build(pointsData);           // 1. 构建图结构
  this.computeLevel(this.pointsData); // 2. 计算层级
  frameScheduler.addTask(() => {
    this.computePointPosition();     // 3. 计算坐标
  });
}
```

### 2.2 关系转换

- **父子关系转换**：将 parentIds 转换为双向关系
- **索引构建**：创建高效的查找映射表
- **层级分配**：使用BFS算法确定节点层级

### 2.3 层级计算算法

```typescript
computeLevel(points: Map<string, Point>) {
  // 1. 计算入度
  degrees = {};
  points.forEach(point => {
    point.childIds.forEach(childId => {
      degrees[childId]++;
    });
  });

  // 2. BFS层级遍历
  queue = []; // 入度为0的节点
  level = 0;
  while (queue.length) {
    // 处理当前层的所有节点
    // 减少子节点的入度
    // 将新的入度为0节点加入下一层
  }
}
```

## 3. 空间布局阶段

### 3.1 3D坐标计算

```typescript
computePointPosition(levelHeight = 15, pointSpace = 7) {
  levels.forEach(level => {
    const points = this.idLevelMap[level];
    const y = -level * levelHeight;
    
    if (points.length === 1) {
      // 单节点：放置在中心
      position = [0, y, 0];
    } else {
      // 多节点：同心圆分布
      distributeInCircles(points, y, pointSpace);
    }
  });
}
```

### 3.2 同心圆分布算法

- **圆环计算**：根据节点数量计算所需圆环数
- **半径递增**：每个圆环半径按pointSpace递增
- **均匀分布**：节点在圆环上均匀分布，避免重叠

## 4. 异步任务调度 (FrameScheduler)

### 4.1 任务分片

```typescript
export class FrameScheduler {
  addTask(task: () => boolean) {
    this.tasks.push(task);
    if (!this.running) {
      this.execute();
    }
  }
  
  private execute() {
    const startTime = performance.now();
    while (this.tasks.length && (performance.now() - startTime) < 16) {
      const task = this.tasks.shift()!;
      const finished = task();
      if (!finished) this.tasks.unshift(task);
    }
    
    if (this.tasks.length) {
      requestAnimationFrame(() => this.execute());
    } else {
      this.onCompletedCallbacks.forEach(cb => cb());
    }
  }
}
```

### 4.2 性能优化

- **时间片分配**：每帧最多执行16ms，避免阻塞UI
- **任务暂停/恢复**：支持长任务的中断和继续
- **完成回调**：异步通知计算完成

## 5. 渲染管线

### 5.1 初始渲染模式

#### 单根模式 (Single Root)
```typescript
firstRenderSignalRootPoints() {
  // 1. 构建动画队列（按层级分组）
  // 2. 渲染节点网格 (KsgPoint)
  // 3. 设置聚焦状态
  // 4. 分层动画进入
  // 5. 连线动画
}
```

#### 多根模式 (Multiple Root)
```typescript
firstRenderMultiplyRootPoints() {
  // 1. 渲染所有节点
  // 2. 设置高亮状态
  // 3. 批量动画进入
  // 4. 无聚焦连线
}
```

### 5.2 增量渲染

```typescript
renderMoreData(focusIndex, diffData) {
  // 1. 加载新节点到GPU缓冲区
  // 2. 处理位置变化的节点动画
  // 3. 更新连线（如需要）
  // 4. 分层动画进入
  // 5. 启用呼吸动画（全局视图）
}
```

## 6. GPU渲染优化

### 6.1 节点渲染 (KsgPoints)

- **BufferGeometry**：高效的顶点数据存储
- **自定义着色器**：GPU并行处理顶点变换
- **实例化渲染**：批量处理大量节点
- **属性动画**：动态修改顶点属性

### 6.2 连线渲染 (KsgLine)

- **LineSegments**：高性能线段渲染
- **流光效果**：着色器实现动态光效
- **顶点颜色插值**：平滑的颜色过渡

## 7. 交互响应流程

### 7.1 聚焦流程

```
用户点击 → 射线检测 → 节点识别 → 数据查询 → 子图构建 → 视角动画 → 连线动画
```

### 7.2 全局视图流程

```
用户触发 → 清理聚焦 → 启用呼吸动画 → 视角切换 → 状态更新
```

## 8. 数据流向图

```mermaid
graph TD
    A[PointData数组] --> B[KsgGraph构建]
    B --> C[关系转换]
    C --> D[层级计算BFS]
    D --> E[坐标计算]
    E --> F[FrameScheduler异步]
    F --> G{渲染模式}
    G -->|单根| H[聚焦渲染]
    G -->|多根| I[全局渲染]
    H --> J[节点动画]
    I --> J
    J --> K[连线动画]
    K --> L[用户交互]
    L --> M{交互类型}
    M -->|点击| N[聚焦切换]
    M -->|全局| O[全局视图]
    N --> P[增量加载]
    P --> G
```

## 9. 性能关键点

### 9.1 内存优化

- **对象池**：复用几何体和材质
- **缓冲区预分配**：避免频繁内存分配
- **及时释放**：清理不再使用的资源

### 9.2 渲染优化

- **视锥剔除**：只渲染可见节点
- **LOD系统**：根据距离调整细节
- **批处理**：减少Draw Call数量

### 9.3 计算优化

- **异步计算**：避免阻塞主线程
- **增量更新**：只计算变化的部分
- **空间索引**：加速空间查询

