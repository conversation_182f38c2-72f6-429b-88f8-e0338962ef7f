/**
 * 数据加载器 - KsgMap 知识图谱数据管理核心模块
 *
 * 该模块负责知识图谱节点数据的分层加载、缓存管理和状态维护
 * 支持分页加载、数据预处理、错误处理等功能
 * 是 KsgMap 可视化组件的数据基础设施
 */

import { POINT_STUDY_STATUS } from "../enums/index";
import type { PointData, getSignalRootApi } from "../types";
import type { Ref } from "@vue/reactivity";
import { reFormateTitle, injectMathJax } from "../utils/mathJax";

/**
 * 数据加载器 Hook - 处理知识图谱节点数据的分层加载
 *
 * 功能特性：
 * - 支持分层级分页加载知识图谱数据
 * - 内置数据缓存机制，避免重复请求
 * - 自动处理 MathJax 公式渲染
 * - 统一的加载状态管理
 * - 数据预处理和格式化
 *
 * @param api - 数据获取 API 函数，用于请求后端知识图谱数据
 * @param rootId - 根节点 ID，作为数据加载的起始点
 * @param loading - 响应式加载状态引用，管理 loading/loaded/error 状态
 * @param crt - 当前加载层级，默认从第 1 层开始
 * @param levelSize - 每次加载的层级数量，默认加载 2 层数据
 * @returns 包含初始化、加载更多数据方法的对象
 */
export default function dataLoader(
  api: getSignalRootApi,
  rootId: string,
  loading: Ref<"loading" | "loaded" | "error">,
  crt: number = 1,
  levelSize: number = 2
) {
  // 节点数据缓存数组 - 存储已加载的知识图谱节点数据
  const cache: PointData[] = [];
  // 总节点数 - 后端返回的数据总量，用于判断是否还有更多数据
  let totalPoints: number = 0;
  // 当前已加载节点数 - 用于分页控制和加载判断
  let pointCount: number = 0;

  /**
   * 初始化数据加载 - 首次加载知识图谱数据
   *
   * 执行流程：
   * 1. 设置加载状态为 loading
   * 2. 注入 MathJax 支持（用于渲染数学公式）
   * 3. 调用 API 获取初始数据
   * 4. 数据预处理：设置学习状态、格式化标题
   * 5. 更新缓存和计数器
   * 6. 返回处理后的数据和分页信息
   */

  async function init() {
    loading.value = "loading";
    try {
      // 注入 MathJax 库，用于渲染知识点标题中的数学公式
      // await injectMathJax();

      // 调用 API 获取指定层级的知识图谱数据
      const {
        data: { dataList, total },
      } = await api(crt, levelSize, rootId);

      // 数据预处理和格式化
      cache.push(
        ...dataList.map((item) => {
          // 设置默认学习状态（如果后端未提供）
          item.status = item.status ?? POINT_STUDY_STATUS.unstudy;
          // 格式化节点标题，处理 MathJax 公式
          item.pointName = reFormateTitle(item.pointName);
          return item;
        })
      );

      // 更新统计信息
      pointCount = cache.length;
      totalPoints = total;
      loading.value = "loaded";
    } catch (error) {
      console.error("响应数据异常！无法正常渲染", error);
      loading.value = "error";
    }

    // 返回标准化的数据结构
    return {
      data: cache,
      pager: {
        current: crt,
        levelSize,
        total: totalPoints,
      },
      rootId,
    };
  }

  /**
   * 加载更多数据 - 分页加载额外的知识图谱层级数据（旧版本）
   *
   * 注意：此方法包含被注释的数据合并逻辑，可能用于处理重复数据的层级关系
   *
   * @param rootId - 根节点 ID
   * @param current - 当前页码或层级
   * @param levelSize - 每次加载的层级数量
   * @returns 返回新加载的数据列表
   */
  async function loadMore(rootId: string, current: number, levelSize: number) {
    // 检查是否还有更多数据需要加载
    if (pointCount >= totalPoints) return;

    loading.value = "loading";
    try {
      const {
        data: { dataList },
      } = await api(current, levelSize, rootId);

      // 注释的代码：原本用于处理数据层级变化和去重逻辑
      // 由于新的数据加载之前的存在的数据层级会变，重新计算之前的数据结构
      // dataList.forEach((newPoint) => {
      //   newPoint.pointName = reFormateTitle(newPoint.pointName);
      //   newPoint.status = newPoint.status ?? 0;
      //   // 新数据之前是否有返回，有返回则说明该数据层级会变
      //   const index = cache.findIndex(
      //     (oldPoint) => oldPoint.pointId === newPoint.pointId
      //   );
      //   index > -1
      //     ? cache[index].parentPointIds.push(...newPoint.parentPointIds)
      //     : cache.push(newPoint);
      // });

      loading.value = "loaded";
      return dataList;
    } catch (error) {
      console.error("响应数据异常！无法正常渲染");
      loading.value = "error";
    }

    return [];
  }

  /**
   * 新版本加载更多数据 - 简化的分页加载方法
   *
   * 相比于 loadMore 方法，此版本移除了复杂的数据合并逻辑
   * 直接返回新数据，由调用方处理数据整合
   *
   * @param rootId - 根节点 ID
   * @param current - 当前页码或层级
   * @param levelSize - 每次加载的层级数量
   * @returns 返回新加载的数据列表，如果没有更多数据或出错则返回空数组
   */
  async function loadMoreNew(
    rootId: string,
    current: number,
    levelSize: number
  ) {
    // 数据加载完成检查
    if (pointCount >= totalPoints) return;

    loading.value = "loading";
    try {
      const {
        data: { dataList },
      } = await api(current, levelSize, rootId);
      loading.value = "loaded";
      return dataList;
    } catch (error) {
      console.error("响应数据异常！无法正常渲染");
      loading.value = "error";
    }
    return [];
  }

  // 返回数据加载器的公共接口
  return {
    init, // 初始化数据加载
    loadMore, // 加载更多数据（旧版本，含复杂逻辑）
    loadMoreNew, // 加载更多数据（新版本，简化版）
  };
}
