# Three.js知识图谱 - 速成开发示例
- 速成学习路径 - 核心要点
```mermaid
graph LR
    A[KsgMap.vue<br/>主组件入口] --> B[ctx/index.ts<br/>全局状态]
    B --> C[core/KsgGraph.ts<br/>算法核心]
    C --> D[core/KsgPoints.ts<br/>节点渲染]
    D --> E[shader/着色器<br/>视觉效果]
    E --> F[animation/动画<br/>交互体验]
    
```

## 🚀 速成方案：5个核心文件 + 实战案例

### 📋 必读文件清单（按优先级）

#### 🔥 1. KsgMap.vue - 主组件（30分钟）
**作用**：组件入口，理解如何使用和扩展

**关键点**：
- `props.config` - 所有配置都通过这里传入
- `emit('loadMore')` - 数据加载事件
- `emit('clickLabel')` - 节点点击事件

#### 🔥 2. ctx/index.ts - 全局状态（15分钟）

**关键点**：这是全局状态管理，所有Three.js对象都存在这里

#### 🔥 3. core/KsgGraph.ts - 算法核心（45分钟）

**关键点**：
- `compute()` - 计算图结构和坐标
- `loadMore()` - 增量加载数据
- `pointsData` - 存储所有节点信息

#### 🔥 4. core/KsgPoints.ts - 节点渲染（30分钟）

**关键点**：
- `BufferAttribute` - 高性能顶点属性
- `updatePosition/Color/Size` - 动态更新节点
- `idIndexMap` - ID到索引的映射

#### 🔥 5. shader/pointFrag.glsl - 着色器效果（20分钟）

**关键点**：
- `vIsBreathAni` - 控制呼吸动画
- `uTime` - 时间参数用于动画
- `vColor` - 节点颜色



## 1. 修改节点颜色和大小

### 场景：根据节点状态显示不同颜色
```typescript
// 在 core/KsgPoints.ts 中修改
updateNodesByStatus() {
  this.pointsData.forEach((point, index) => {
    let color;
    let size;
    
    switch(point.status) {
      case 0: // 未学习
        color = new Color(0x888888); // 灰色
        size = 8;
        break;
      case 1: // 学习中
        color = new Color(0xffaa00); // 橙色
        size = 12;
        break;
      case 2: // 已完成
        color = new Color(0x00ff00); // 绿色
        size = 10;
        break;
      default:
        color = new Color(0x0088ff); // 蓝色
        size = 10;
    }
    
    this.updateColor([index], color);
    this.updateSize([index], size);
  });
}
```

### 使用方法：
```typescript
// 在 KsgMap.vue 中调用
const pointsMesh = ctx.pointsMesh;
if (pointsMesh) {
  pointsMesh.updateNodesByStatus();
}
```

## 2. 添加新的动画效果

### 场景：节点闪烁提醒效果
```typescript
// 在 animation/ 目录下创建 blink.ts
import TWEEN from "@tweenjs/tween.js";
import type KsgPoint from "../core/KsgPoints";

export function blinkAnimation(
  pointsMesh: KsgPoint,
  nodeIds: string[],
  duration: number = 1000
): Promise<void> {
  return new Promise((resolve) => {
    const indices = nodeIds.map(id => pointsMesh.idIndexMap[id]).filter(i => i !== undefined);
    
    // 闪烁动画：透明度 1 -> 0.3 -> 1
    new TWEEN.Tween({ opacity: 1 })
      .to({ opacity: 0.3 }, duration / 2)
      .easing(TWEEN.Easing.Cubic.InOut)
      .onUpdate((values) => {
        pointsMesh.updateOpacity(indices, values.opacity);
      })
      .chain(
        new TWEEN.Tween({ opacity: 0.3 })
          .to({ opacity: 1 }, duration / 2)
          .easing(TWEEN.Easing.Cubic.InOut)
          .onUpdate((values) => {
            pointsMesh.updateOpacity(indices, values.opacity);
          })
          .onComplete(resolve)
      )
      .start();
  });
}
```

### 使用方法：
```typescript
// 在需要的地方调用
import { blinkAnimation } from './animation/blink';

// 让特定节点闪烁
blinkAnimation(ctx.pointsMesh!, ['node1', 'node2'], 2000);
```

## 3. 自定义节点形状

### 场景：里程碑节点显示为星形
```glsl
// 修改 shader/pointFrag.glsl
varying float vIsMilestone; // 新增：是否为里程碑

void main() {
    vec2 center = gl_PointCoord - 0.5;
    float dist = length(center);
    
    if(vIsMilestone == 1.0) {
        // 星形形状计算
        float angle = atan(center.y, center.x);
        float starRadius = 0.3 + 0.1 * cos(5.0 * angle);
        
        if(dist > starRadius) {
            discard; // 丢弃星形外的像素
        }
        
        // 星形颜色
        gl_FragColor = vec4(1.0, 1.0, 0.0, vOpacity); // 金色
    } else {
        // 原有的圆形逻辑
        vec4 texColor = texture2D(map, gl_PointCoord + offset);
        gl_FragColor = vec4(vColor * texColor.rgb, texColor.a * vOpacity);
    }
}
```

```typescript
// 在 KsgPoints.ts 中添加里程碑属性
const milestoneAttribute = new BufferAttribute(new Float32Array(total), 1);
points.forEach((point, index) => {
  milestoneAttribute.setX(index, point.isMilestone ? 1.0 : 0.0);
});
pGeo.setAttribute('isMilestone', milestoneAttribute);
```

## 4. 添加搜索高亮功能

### 场景：搜索节点并高亮显示
```typescript
// 在 KsgMap.vue 中添加搜索方法
const searchNodes = (keyword: string) => {
  if (!ctx.pointsMesh) return;
  
  // 重置所有节点颜色
  ctx.pointsMesh.pointsData.forEach((point, index) => {
    ctx.pointsMesh!.updateColor([index], new Color(0x0088ff));
  });
  
  // 查找匹配的节点
  const matchedIndices: number[] = [];
  ctx.pointsMesh.pointsData.forEach((point, index) => {
    if (point.name.toLowerCase().includes(keyword.toLowerCase())) {
      matchedIndices.push(index);
    }
  });
  
  // 高亮匹配的节点
  if (matchedIndices.length > 0) {
    ctx.pointsMesh.updateColor(matchedIndices, new Color(0xff0000)); // 红色高亮
    ctx.pointsMesh.updateSize(matchedIndices, 15); // 放大
  }
  
  return matchedIndices.length;
};

// 暴露给父组件
defineExpose({
  searchNodes
});
```

### 使用方法：
```vue
<!-- 在 App.vue 中 -->
<template>
  <div>
    <input v-model="searchKeyword" @input="handleSearch" placeholder="搜索节点...">
    <KsgMap ref="ksgMapRef" :config="config" />
  </div>
</template>

<script setup>
const searchKeyword = ref('');
const ksgMapRef = ref();

const handleSearch = () => {
  if (ksgMapRef.value && searchKeyword.value) {
    const count = ksgMapRef.value.searchNodes(searchKeyword.value);
    console.log(`找到 ${count} 个匹配节点`);
  }
};
</script>
```

## 5. 添加节点详情弹窗

### 场景：点击节点显示详细信息
```typescript
// 在 KsgMap.vue 中添加弹窗状态
const showNodeDetail = ref(false);
const selectedNode = ref<Point | null>(null);

// 修改点击事件处理
const handleClickLabel = (id: string) => {
  const node = ctx.pointsMesh?.getPointDataById(id);
  if (node) {
    selectedNode.value = node;
    showNodeDetail.value = true;
  }
  emit("clickLabel", id);
};
```

```vue
<!-- 在 KsgMap.vue 模板中添加弹窗 -->
<template>
  <div class="ksg-three-container">
    <!-- 原有内容 -->
    
    <!-- 节点详情弹窗 -->
    <div v-if="showNodeDetail" class="node-detail-modal" @click="showNodeDetail = false">
      <div class="modal-content" @click.stop>
        <h3>{{ selectedNode?.name }}</h3>
        <p>状态: {{ getStatusText(selectedNode?.status) }}</p>
        <p>层级: {{ selectedNode?.level }}</p>
        <p>是否里程碑: {{ selectedNode?.isMilestone ? '是' : '否' }}</p>
        <button @click="showNodeDetail = false">关闭</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.node-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  min-width: 300px;
}
</style>
```

## 🚀 快速上手步骤

### 1. 理解数据流（5分钟）
```
App.vue → KsgMap.vue → KsgGraph → KsgPoints → 着色器 → 屏幕
```

### 2. 找到关键对象（5分钟）
```typescript
import ctx from './ctx'; // 全局状态
ctx.pointsMesh  // 节点对象
ctx.scene      // Three.js场景
ctx.camera     // 相机
```

### 3. 常用修改点（10分钟）
- **节点外观**: `core/KsgPoints.ts` + `shader/pointFrag.glsl`
- **动画效果**: `animation/` 目录
- **交互逻辑**: `config/event.ts`
- **数据处理**: `core/KsgGraph.ts`

### 4. 调试技巧（5分钟）
```typescript
// 在浏览器控制台中
window.ctx = ctx; // 暴露全局状态
console.log(ctx.pointsMesh.pointsData); // 查看节点数据
```

## 💡 开发建议

1. **先改参数再改逻辑** - 从修改颜色、大小开始
2. **善用浏览器调试** - 使用Performance面板分析性能
3. **参考现有动画** - 复制 `animation/load.ts` 的模式
4. **测试小范围** - 先在少量节点上测试效果
5. **保持备份** - 修改前备份原始文件

通过这些示例，你可以快速上手并实现常见的二次开发需求！
