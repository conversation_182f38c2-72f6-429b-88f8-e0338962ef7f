import type { PointData, getSignalRootApi } from "../types";
import type { Ref } from "@vue/reactivity";
/**
 *处理单节点模式，处理分页获取额数据
 * @param api 获取请求的数据
 * @param rootId 根节点id
 * @param loading  loading状态
 * @param crt 从那层开始获取数据
 * @param levelSize  获取多少层
 */
export default function dataLoader(api: getSignalRootApi, rootId: string, loading: Ref<"loading" | "loaded" | "error">, crt?: number, levelSize?: number): {
    init: () => Promise<{
        data: PointData[];
        pager: {
            current: number;
            levelSize: number;
            total: number;
        };
        rootId: string;
    }>;
    loadMore: (rootId: string, current: number, levelSize: number) => Promise<PointData[] | undefined>;
    loadMoreNew: (rootId: string, current: number, levelSize: number) => Promise<PointData[] | undefined>;
};
