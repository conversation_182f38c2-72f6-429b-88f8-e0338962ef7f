# Three.js 实践练习项目

## 练习项目规划

通过循序渐进的小项目来掌握 Three.js 技能，每个项目都对应 KsgMap 中的核心技术。

## 项目一：基础场景搭建（1小时）

### 目标
理解 Three.js 的基本架构，对应 KsgMap 的配置系统。

### 实现内容
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Three.js 基础场景</title>
    <style>
        body { margin: 0; overflow: hidden; }
        canvas { display: block; }
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            font-family: Arial;
        }
    </style>
</head>
<body>
    <div id="info">
        <div>鼠标左键：旋转</div>
        <div>鼠标右键：平移</div>
        <div>滚轮：缩放</div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r150/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r150/controls/OrbitControls.js"></script>
    
    <script>
        // 1. 创建基础三要素
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        const renderer = new THREE.WebGLRenderer({ antialias: true });
        
        // 2. 配置渲染器
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setPixelRatio(window.devicePixelRatio);
        renderer.setClearColor(0x000011);
        document.body.appendChild(renderer.domElement);
        
        // 3. 添加相机控制器（对应 KsgMap 的 KsgControls）
        const controls = new THREE.OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.05;
        
        // 4. 创建一些基础对象
        const geometry = new THREE.BoxGeometry();
        const material = new THREE.MeshBasicMaterial({ 
            color: 0x00ff00,
            wireframe: true 
        });
        const cube = new THREE.Mesh(geometry, material);
        scene.add(cube);
        
        // 5. 设置相机位置
        camera.position.set(5, 5, 5);
        camera.lookAt(0, 0, 0);
        
        // 6. 渲染循环
        function animate() {
            requestAnimationFrame(animate);
            
            // 更新控制器
            controls.update();
            
            // 渲染场景
            renderer.render(scene, camera);
        }
        
        // 7. 响应式处理
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        animate();
    </script>
</body>
</html>
```

### 学习要点
- 理解 Scene、Camera、Renderer 的作用
- 掌握 OrbitControls 的使用
- 理解渲染循环的概念
- 学会响应式处理

### 对应 KsgMap 代码
- `config/scene.ts`
- `config/camera.ts`
- `config/renderer.ts`
- `config/controls.ts`

## 项目二：点云渲染系统（2小时）

### 目标
理解 BufferGeometry 和 Points 对象，对应 KsgMap 的节点渲染。

### 实现内容
```javascript
// 创建点云渲染器
class PointCloudRenderer {
    constructor(scene) {
        this.scene = scene;
        this.points = null;
    }
    
    // 创建随机点云数据
    generatePointsData(count = 1000) {
        const positions = new Float32Array(count * 3);
        const colors = new Float32Array(count * 3);
        const sizes = new Float32Array(count);
        
        for (let i = 0; i < count; i++) {
            // 位置：在球体内随机分布
            const radius = Math.random() * 10;
            const theta = Math.random() * Math.PI * 2;
            const phi = Math.random() * Math.PI;
            
            positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
            positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
            positions[i * 3 + 2] = radius * Math.cos(phi);
            
            // 颜色：根据位置生成
            colors[i * 3] = Math.random();
            colors[i * 3 + 1] = Math.random();
            colors[i * 3 + 2] = Math.random();
            
            // 大小：随机
            sizes[i] = Math.random() * 5 + 1;
        }
        
        return { positions, colors, sizes };
    }
    
    // 创建点云对象
    createPoints(pointsData) {
        const geometry = new THREE.BufferGeometry();
        
        // 设置顶点属性
        geometry.setAttribute('position', 
            new THREE.BufferAttribute(pointsData.positions, 3));
        geometry.setAttribute('color', 
            new THREE.BufferAttribute(pointsData.colors, 3));
        geometry.setAttribute('size', 
            new THREE.BufferAttribute(pointsData.sizes, 1));
        
        // 创建材质
        const material = new THREE.PointsMaterial({
            size: 2,
            vertexColors: true,
            transparent: true,
            opacity: 0.8,
            sizeAttenuation: true  // 距离衰减
        });
        
        // 创建点云对象
        this.points = new THREE.Points(geometry, material);
        this.scene.add(this.points);
        
        return this.points;
    }
    
    // 更新点的颜色（模拟 KsgMap 的状态变化）
    updatePointColor(index, color) {
        const colors = this.points.geometry.attributes.color;
        colors.setXYZ(index, color.r, color.g, color.b);
        colors.needsUpdate = true;
    }
    
    // 动画更新
    animate() {
        if (this.points) {
            this.points.rotation.y += 0.005;
        }
    }
}

// 使用示例
const pointRenderer = new PointCloudRenderer(scene);
const pointsData = pointRenderer.generatePointsData(2000);
const pointsObject = pointRenderer.createPoints(pointsData);

// 在渲染循环中调用
function animate() {
    requestAnimationFrame(animate);
    pointRenderer.animate();
    controls.update();
    renderer.render(scene, camera);
}
```

### 学习要点
- BufferGeometry 的属性设置
- Points 对象的创建和配置
- 顶点属性的动态更新
- PointsMaterial 的参数理解

### 对应 KsgMap 代码
- `core/KsgPoints.ts`

## 项目三：自定义着色器入门（2小时）

### 目标
理解着色器的基本概念，为学习 KsgMap 的着色器系统做准备。

### 顶点着色器
```glsl
// vertex shader
attribute float size;
attribute vec3 customColor;
varying vec3 vColor;

void main() {
    vColor = customColor;
    
    vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
    gl_PointSize = size * (300.0 / -mvPosition.z);
    gl_Position = projectionMatrix * mvPosition;
}
```

### 片段着色器
```glsl
// fragment shader
uniform float time;
varying vec3 vColor;

void main() {
    // 创建圆形点
    vec2 center = gl_PointCoord - vec2(0.5);
    float dist = length(center);
    
    if (dist > 0.5) {
        discard;
    }
    
    // 添加呼吸效果
    float pulse = sin(time * 2.0) * 0.3 + 0.7;
    vec3 finalColor = vColor * pulse;
    
    gl_FragColor = vec4(finalColor, 1.0 - dist * 2.0);
}
```

### JavaScript 代码
```javascript
// 自定义着色器材质
const shaderMaterial = new THREE.ShaderMaterial({
    uniforms: {
        time: { value: 0.0 }
    },
    vertexShader: vertexShaderSource,
    fragmentShader: fragmentShaderSource,
    transparent: true,
    vertexColors: true
});

// 在渲染循环中更新时间
function animate() {
    shaderMaterial.uniforms.time.value = performance.now() * 0.001;
    // ... 其他代码
}
```

### 学习要点
- 着色器的基本语法
- uniform、attribute、varying 的区别
- 如何在 JavaScript 中传递数据给着色器
- 简单的着色器动画

### 对应 KsgMap 代码
- `shader/pointVert.glsl`
- `shader/pointFrag.glsl`

## 项目四：线条渲染和动画（1.5小时）

### 目标
理解线条渲染和流光动画，对应 KsgMap 的连线系统。

### 实现内容
```javascript
class LineRenderer {
    constructor(scene) {
        this.scene = scene;
        this.lines = [];
    }
    
    // 创建连接两点的线条
    createLine(startPoint, endPoint, color = 0xffffff) {
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array([
            startPoint.x, startPoint.y, startPoint.z,
            endPoint.x, endPoint.y, endPoint.z
        ]);
        
        geometry.setAttribute('position', 
            new THREE.BufferAttribute(positions, 3));
        
        const material = new THREE.LineBasicMaterial({ 
            color: color,
            transparent: true,
            opacity: 0.6
        });
        
        const line = new THREE.Line(geometry, material);
        this.scene.add(line);
        this.lines.push(line);
        
        return line;
    }
    
    // 创建多条连线（模拟知识图谱的连接关系）
    createNetwork(nodes) {
        // 随机连接节点
        for (let i = 0; i < nodes.length - 1; i++) {
            for (let j = i + 1; j < nodes.length; j++) {
                if (Math.random() > 0.7) {  // 30% 的概率创建连线
                    this.createLine(nodes[i], nodes[j], 
                        new THREE.Color().setHSL(Math.random(), 0.7, 0.5));
                }
            }
        }
    }
    
    // 简单的流光动画
    animateLines() {
        this.lines.forEach((line, index) => {
            const material = line.material;
            const time = performance.now() * 0.001;
            material.opacity = 0.3 + Math.sin(time + index) * 0.3;
        });
    }
}

// 使用示例
const lineRenderer = new LineRenderer(scene);

// 创建一些节点
const nodes = [];
for (let i = 0; i < 20; i++) {
    nodes.push(new THREE.Vector3(
        (Math.random() - 0.5) * 20,
        (Math.random() - 0.5) * 20,
        (Math.random() - 0.5) * 20
    ));
}

// 创建网络连接
lineRenderer.createNetwork(nodes);

// 在渲染循环中更新动画
function animate() {
    lineRenderer.animateLines();
    // ... 其他代码
}
```

### 学习要点
- Line 和 LineSegments 的区别
- 线条材质的配置
- 简单的动画实现
- 网络图的基本概念

### 对应 KsgMap 代码
- `core/KsgLine.ts`
- `shader/lineVert.glsl`
- `shader/lineFrag.glsl`

## 项目五：交互系统实现（2小时）

### 目标
理解射线投射和事件处理，对应 KsgMap 的交互系统。

### 实现内容
```javascript
class InteractionManager {
    constructor(camera, scene, renderer) {
        this.camera = camera;
        this.scene = scene;
        this.renderer = renderer;
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        this.hoveredObject = null;
        this.selectedObject = null;
        
        this.initEvents();
    }
    
    initEvents() {
        this.renderer.domElement.addEventListener('mousemove', 
            this.onMouseMove.bind(this));
        this.renderer.domElement.addEventListener('click', 
            this.onClick.bind(this));
    }
    
    onMouseMove(event) {
        // 将鼠标坐标转换为标准化设备坐标
        const rect = this.renderer.domElement.getBoundingClientRect();
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
        
        // 更新射线
        this.raycaster.setFromCamera(this.mouse, this.camera);
        
        // 检测相交对象
        const intersects = this.raycaster.intersectObjects(this.scene.children, true);
        
        // 处理悬停效果
        if (intersects.length > 0) {
            const object = intersects[0].object;
            if (object !== this.hoveredObject) {
                this.onHoverEnter(object);
            }
        } else {
            if (this.hoveredObject) {
                this.onHoverLeave(this.hoveredObject);
            }
        }
    }
    
    onClick(event) {
        const intersects = this.raycaster.intersectObjects(this.scene.children, true);
        if (intersects.length > 0) {
            const object = intersects[0].object;
            this.onObjectClick(object, intersects[0].point);
        }
    }
    
    onHoverEnter(object) {
        if (this.hoveredObject) {
            this.onHoverLeave(this.hoveredObject);
        }
        
        this.hoveredObject = object;
        
        // 改变材质颜色表示悬停
        if (object.material) {
            object.userData.originalColor = object.material.color.clone();
            object.material.color.setHex(0xff0000);
        }
        
        // 改变鼠标样式
        this.renderer.domElement.style.cursor = 'pointer';
    }
    
    onHoverLeave(object) {
        // 恢复原始颜色
        if (object.material && object.userData.originalColor) {
            object.material.color.copy(object.userData.originalColor);
        }
        
        this.hoveredObject = null;
        this.renderer.domElement.style.cursor = 'default';
    }
    
    onObjectClick(object, point) {
        console.log('点击了对象:', object);
        console.log('点击位置:', point);
        
        // 选中效果
        if (this.selectedObject && this.selectedObject !== object) {
            // 取消之前的选中状态
            this.selectedObject.scale.setScalar(1);
        }
        
        this.selectedObject = object;
        object.scale.setScalar(1.2);  // 放大表示选中
    }
}

// 使用示例
const interactionManager = new InteractionManager(camera, scene, renderer);
```

### 学习要点
- Raycaster 的使用方法
- 鼠标坐标转换
- 对象状态管理
- 事件处理模式

### 对应 KsgMap 代码
- `config/event.ts`
- `utils/clickPointEvent.ts`
- `utils/hoverObjectEvent.ts`

## 项目六：动画系统集成（1.5小时）

### 目标
理解 TWEEN.js 和动画管理，对应 KsgMap 的动画系统。

### 实现内容
```html
<!-- 引入 TWEEN.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/tween.js/18.6.4/tween.umd.js"></script>

<script>
class AnimationManager {
    constructor() {
        this.animations = new Map();
    }
    
    // 相机飞行动画
    flyToObject(camera, controls, targetObject, duration = 1000) {
        const startPosition = camera.position.clone();
        const startTarget = controls.target.clone();
        
        // 计算目标位置
        const targetPosition = targetObject.position.clone();
        targetPosition.add(new THREE.Vector3(5, 5, 5));
        
        const tween = new TWEEN.Tween({
            x: startPosition.x,
            y: startPosition.y,
            z: startPosition.z,
            targetX: startTarget.x,
            targetY: startTarget.y,
            targetZ: startTarget.z
        })
        .to({
            x: targetPosition.x,
            y: targetPosition.y,
            z: targetPosition.z,
            targetX: targetObject.position.x,
            targetY: targetObject.position.y,
            targetZ: targetObject.position.z
        }, duration)
        .easing(TWEEN.Easing.Cubic.InOut)
        .onUpdate((coords) => {
            camera.position.set(coords.x, coords.y, coords.z);
            controls.target.set(coords.targetX, coords.targetY, coords.targetZ);
            controls.update();
        })
        .start();
        
        return tween;
    }
    
    // 对象出现动画
    objectAppearAnimation(object, duration = 500) {
        object.scale.setScalar(0);
        object.material.opacity = 0;
        
        const tween = new TWEEN.Tween({
            scale: 0,
            opacity: 0
        })
        .to({
            scale: 1,
            opacity: 1
        }, duration)
        .easing(TWEEN.Easing.Back.Out)
        .onUpdate((values) => {
            object.scale.setScalar(values.scale);
            object.material.opacity = values.opacity;
        })
        .start();
        
        return tween;
    }
    
    // 脉冲动画
    pulseAnimation(object, duration = 1000) {
        const originalScale = object.scale.x;
        
        const tween = new TWEEN.Tween({ scale: originalScale })
        .to({ scale: originalScale * 1.2 }, duration / 2)
        .easing(TWEEN.Easing.Sinusoidal.InOut)
        .onUpdate((values) => {
            object.scale.setScalar(values.scale);
        })
        .yoyo(true)
        .repeat(Infinity)
        .start();
        
        this.animations.set(object.uuid, tween);
        return tween;
    }
    
    // 停止对象的所有动画
    stopObjectAnimations(object) {
        const tween = this.animations.get(object.uuid);
        if (tween) {
            tween.stop();
            this.animations.delete(object.uuid);
        }
    }
    
    // 更新所有动画
    update() {
        TWEEN.update();
    }
}

// 使用示例
const animationManager = new AnimationManager();

// 在渲染循环中更新动画
function animate() {
    requestAnimationFrame(animate);
    animationManager.update();
    controls.update();
    renderer.render(scene, camera);
}

// 点击对象时触发动画
interactionManager.onObjectClick = function(object, point) {
    // 相机飞向对象
    animationManager.flyToObject(camera, controls, object);
    
    // 对象脉冲动画
    animationManager.pulseAnimation(object);
};
</script>
```

### 学习要点
- TWEEN.js 的基本使用
- 缓动函数的选择
- 动画的组合和管理
- 相机动画的实现

### 对应 KsgMap 代码
- `animation/` 目录下的所有文件
- `hooks/useRendererFrame.ts`

## 综合项目：简化版知识图谱（3-4小时）

### 目标
整合所有学到的技术，创建一个简化版的知识图谱。

### 项目结构
```
simple-knowledge-graph/
├── index.html
├── js/
│   ├── GraphRenderer.js      # 图渲染器
│   ├── NodeManager.js        # 节点管理器
│   ├── LineManager.js        # 连线管理器
│   ├── InteractionManager.js # 交互管理器
│   └── AnimationManager.js   # 动画管理器
└── data/
    └── sample-data.json      # 示例数据
```

### 核心功能
1. **数据加载**：从 JSON 文件加载节点和连接关系
2. **布局算法**：简单的力导向布局或层次布局
3. **节点渲染**：不同类型节点用不同颜色和大小
4. **连线渲染**：显示节点间的关系
5. **交互功能**：点击节点聚焦，悬停显示信息
6. **动画效果**：节点出现动画，相机移动动画

### 数据格式
```json
{
  "nodes": [
    {
      "id": "1",
      "name": "Three.js",
      "type": "technology",
      "level": 0
    },
    {
      "id": "2", 
      "name": "WebGL",
      "type": "technology",
      "level": 1
    }
  ],
  "links": [
    {
      "source": "1",
      "target": "2",
      "type": "depends_on"
    }
  ]
}
```

## 学习时间安排建议

### 第一周：基础概念
- 项目一：基础场景搭建（1小时）
- 项目二：点云渲染系统（2小时）
- 复习和练习（2小时）

### 第二周：进阶技术
- 项目三：自定义着色器入门（2小时）
- 项目四：线条渲染和动画（1.5小时）
- 复习和练习（1.5小时）

### 第三周：交互和动画
- 项目五：交互系统实现（2小时）
- 项目六：动画系统集成（1.5小时）
- 复习和练习（1.5小时）

### 第四周：综合应用
- 综合项目：简化版知识图谱（3-4小时）
- 开始阅读 KsgMap 源码（1小时）

## 总结

通过这些循序渐进的练习项目，您将：

1. **掌握 Three.js 基础**：场景、相机、渲染器的使用
2. **理解高性能渲染**：BufferGeometry、Points、着色器
3. **学会交互处理**：射线投射、事件管理
4. **掌握动画技术**：TWEEN.js、相机动画
5. **具备项目经验**：完整的3D应用开发流程

完成这些练习后，您就具备了阅读和理解 KsgMap 源码的基础知识，可以更好地学习其高级技术和优化策略。
