/**
 * 知识图谱专用控制器 - KsgControls.ts
 * 
 * 职责：
 * 1. 管理3D场景中的相机控制逻辑
 * 2. 支持多种控制模式（知识点层/领域层）
 * 3. 提供平滑的交互体验（阻尼、限制等）
 * 4. 处理鼠标、触摸、键盘等多种输入方式
 * 5. 集成自动旋转和控制状态检测
 *    
 * 技术特点：
 * - 基于Three.js的球面坐标系统
 * - 支持多点触控操作
 * - 自适应Y轴范围限制
 * - 事件驱动的架构设计
 * - 高性能的实时更新机制
 */

// 导入Three.js核心模块和类型定义
import {
  EventDispatcher,    // 事件分发器基类，用于实现观察者模式
  PerspectiveCamera, // 透视相机，模拟人眼视觉效果
  MOUSE,            // 鼠标按键枚举
  Quaternion,       // 四元数，用于3D旋转计算
  Spherical,        // 球面坐标系，用于相机轨道控制
  TOUCH,            // 触摸手势枚举
  Vector2,          // 二维向量，用于屏幕坐标计算
  Vector3,          // 三维向量，用于3D空间坐标
  type BaseEvent,   // 基础事件类型
  Matrix4,          // 4x4矩阵，用于3D变换
  Group,            // 3D对象组
  Object3D,         // 3D对象基类
  Vector4,          // 四维向量
} from "three";
// 导入工具函数
import { cmap } from "../utils";
// 导入自动旋转管理器
import { AutoRotateManager, type AutoRotateManagerOptions } from "./AutoRotateManager";

/**
 * 控制器模式枚举
 * 定义了不同的相机控制模式，适应不同的使用场景
 */
enum KsgMode {
  /** 知识点层模式 - 标准的节点浏览模式 */
  Star,
  /** 领域层模式 - 领域级别的宏观控制模式 */
  Domain,
}

// 预定义的事件对象，避免频繁创建对象，提高性能
const _startEvent = { type: "start" } as BaseEvent;    // 控制操作开始事件
const _endEvent = { type: "end" } as BaseEvent;        // 控制操作结束事件
const _changeEvent = { type: "change" } as BaseEvent;  // 相机状态变化事件

/**
 * 知识图谱专用控制器类
 * 继承自Three.js的EventDispatcher，支持事件驱动的交互模式
 */
class KsgControls extends EventDispatcher<{ [key: string]: BaseEvent }> {
  // === 核心组件引用 ===
  /** 控制的透视相机对象 */
  object: PerspectiveCamera;
  /** 根领域对象组 - 用于领域层模式的控制 */
  rootAreaObj: Group;
  /** 子领域对象 - 领域模式下的控制目标 */
  subareas: Object3D | null;
  /** DOM元素 - 用于事件监听和交互 */
  domElement: HTMLElement;

  // === 基础控制属性 ===
  /** 控制器总开关 */
  enabled: boolean;
  /** 相机观察目标点 - 相机总是朝向这个点 */
  target: Vector3;
  
  // === 距离控制 ===
  /** 相机距离目标的最小值 - 防止过度靠近 */
  minDistance: number;
  /** 相机距离目标的最大值 - 防止无限远离 */
  maxDistance: number;
  
  // === 角度控制 ===
  /** 极角的最小值 - 限制垂直旋转范围 */
  minPolarAngle: number;
  /** 极角的最大值 - 限制垂直旋转范围 */
  maxPolarAngle: number;
  
  // === 阻尼系统 ===
  /** 是否启用阻尼效果 - 提供平滑的控制体验 */
  enableDamping: boolean;
  /** 阻尼系数 - 控制减速的程度 */
  dampingFactor: number;
  
  // === 功能开关 ===
  /** 是否允许缩放操作 */
  enableZoom: boolean;
  /** 缩放速度倍数 */
  zoomSpeed: number;
  /** 是否允许旋转操作 */
  enableRotate: boolean;
  /** 旋转速度倍数 */
  rotateSpeed: number;
  /** 是否允许平移操作 */
  enablePan: boolean;
  /** 平移速度倍数 */
  panSpeed: number;
  
  // === 模式控制 ===
  /** 当前控制模式 */
  mode: KsgMode;
  
  // === 输入映射 ===
  /** 键盘按键映射 */
  keys: { LEFT: string; UP: string; RIGHT: string; BOTTOM: string };
  /** 鼠标按键功能映射 */
  mouseButtons: { LEFT: MOUSE; MIDDLE: MOUSE; RIGHT: MOUSE };
  /** 触摸手势映射 */
  touches: { ONE: TOUCH; TWO: TOUCH };
  /** 键盘控制的移动速度 */
  keyPanSpeed: number;
  
  // === Y轴范围控制 ===
  /** 相机Y轴的最小范围 - 适应知识图谱的纵向结构 */
  yMinRange: number;
  /** 相机Y轴的最大范围 */
  yMaxRange: number;
  /** 目标点Y轴的范围偏移量 */
  yDelta: number;
  /** Y轴方向向量 */
  yAxis: Vector3;

  // === 自动功能 ===
  /** 是否启用自动旋转 */
  autoRotate: boolean;
  /** 自动旋转的速度 */
  autoRotateSpeed: number;
  /** 自动旋转管理器 */
  autoRotateManager?: AutoRotateManager;

  // === 状态检测 ===
  /** 是否正在进行控制操作 */
  isControls: boolean;
  /** 控制状态检测的计时器 */
  controlsTimer: number | NodeJS.Timeout | null;

  // === 内部状态 ===
  /** 键盘事件监听的DOM元素 */
  _domElementKeyEvents: HTMLElement | null;
  
  // === 方法引用 ===
  /** 更新函数 - 每帧调用以应用控制变化 */
  update: (deltaTime?: number | null) => boolean;
  /** 清理函数 - 移除所有事件监听器 */
  dispose: () => void;
  /** 模式切换函数 - 在不同控制模式间切换 */
  changeMode: (mode: KsgMode) => void;

  /**
   * 构造函数 - 初始化知识图谱控制器
   * 
   * @param object 要控制的透视相机对象
   * @param rootArea 根领域对象组
   * @param domElement 用于事件监听的DOM元素
   */
  constructor(
    object: PerspectiveCamera,
    rootArea: Group,
    domElement: HTMLElement
  ) {
    // 调用父类构造函数，初始化事件分发器
    super();
    
    // 保存核心对象引用
    this.object = object;           // 要控制的相机
    this.rootAreaObj = rootArea;    // 根领域对象组
    this.subareas = null;          // 子领域对象（初始为空）
    this.domElement = domElement;   // DOM事件监听元素
    
    // 禁用DOM元素的默认触摸滚动行为，确保触摸事件被控制器处理
    this.domElement.style.touchAction = "none";

    // 初始化基础控制属性
    this.enabled = true;           // 启用控制器

    // 初始化相机目标和距离限制
    this.target = new Vector3();   // 相机观察目标点（默认原点）
    this.minDistance = 0;          // 最小距离（允许到达目标点）
    this.maxDistance = Infinity;   // 最大距离（无限远）
    
    // 初始化角度限制（弧度制）
    this.minPolarAngle = 0;        // 最小极角（可以看到正上方）
    this.maxPolarAngle = Math.PI;  // 最大极角（可以看到正下方）
    
    // 初始化阻尼系统
    this.enableDamping = false;    // 默认关闭阻尼
    this.dampingFactor = 0.05;     // 阻尼系数（较小的值提供更平滑的减速）
    
    // 初始化功能开关和速度设置
    this.enableZoom = true;        // 启用缩放功能
    this.zoomSpeed = 0.4;          // 缩放速度（较小的值提供更精确的控制）
    this.enableRotate = true;      // 启用旋转功能
    this.rotateSpeed = 1.0;        // 旋转速度（标准速度）
    this.enablePan = true;         // 启用平移功能
    this.panSpeed = 1.0;           // 平移速度（标准速度）
    
    // 设置默认控制模式
    this.mode = KsgMode.Star;      // 默认为知识点层模式
    
    // 配置键盘按键映射（使用箭头键）
    this.keys = {
      LEFT: "ArrowLeft",           // 左箭头键
      UP: "ArrowUp",               // 上箭头键
      RIGHT: "ArrowRight",         // 右箭头键
      BOTTOM: "ArrowDown",         // 下箭头键
    };
    
    // 配置鼠标按键功能映射
    this.mouseButtons = {
      LEFT: MOUSE.ROTATE,          // 左键用于旋转
      MIDDLE: MOUSE.DOLLY,         // 中键用于缩放
      RIGHT: MOUSE.PAN,            // 右键用于平移
    };
    
    // 配置触摸手势映射
    this.touches = { 
      ONE: TOUCH.ROTATE,           // 单指触摸用于旋转
      TWO: TOUCH.DOLLY_PAN         // 双指触摸用于缩放和平移
    };
    
    // 设置键盘控制速度
    this.keyPanSpeed = 7.0;        // 键盘平移速度
    
    // 配置Y轴范围控制（适应知识图谱的垂直结构）
    this.yMinRange = -100;         // Y轴最小范围
    this.yMaxRange = 100;          // Y轴最大范围
    this.yDelta = 100;             // Y轴偏移量
    this.yAxis = new Vector3(0, 0, 0); // Y轴方向向量
    
    // 初始化键盘事件监听元素引用
    this._domElementKeyEvents = null;

    // 初始化自动旋转功能
    this.autoRotate = false;       // 默认关闭自动旋转
    this.autoRotateSpeed = 2.0;    // 自动旋转速度

    // 初始化控制状态检测
    this.isControls = false;       // 当前未在控制中
    this.controlsTimer = null;     // 控制状态检测计时器

    // 初始化自动旋转管理器（在所有事件监听器设置完成后）
    // 延迟初始化以确保控制器完全设置完成
    setTimeout(() => {
      this.autoRotateManager = new AutoRotateManager(this, {
        restartDelay: 5000,        // 5秒后重新启动自动旋转
        autoStart: true,           // 初始化时自动启动
        rotateSpeed: 2.0,          // 自动旋转速度
        debug: false               // 关闭调试日志
      });
    }, 100);

    // 定义核心的更新方法，使用闭包模式避免频繁创建临时变量
    this.update = (function () {
      // 创建局部变量，避免每次调用时重复创建对象
      const offset = new Vector3();       // 相机位置相对于目标的偏移向量

      // 创建四元数用于坐标系转换（使Y轴向上作为轨道轴）
      const quat = new Quaternion().setFromUnitVectors(
        object.up,               // 相机的上方向
        new Vector3(0, 1, 0)    // 世界坐标系的Y轴向上
      );
      const quatInverse = quat.clone().invert(); // 逆四元数，用于反向转换
      
      // 缓存上一帧的状态，用于检测变化
      const lastPosition = new Vector3();      // 上一帧相机位置
      const lastQuaternion = new Quaternion(); // 上一帧相机旋转
      const lastTargetPosition = new Vector3(); // 上一帧目标位置
      const twoPI = 2 * Math.PI;               // 2π常量

      /**
       * 实际的更新函数 - 每帧调用以应用所有控制变化
       * @param deltaTime 可选的时间增量，用于基于时间的动画
       * @returns boolean 如果相机状态发生变化返回true
       */
      return function update(deltaTime: number | null = null) {
        // 获取当前相机位置
        const position = scope.object.position;
        // 计算相机相对于目标的偏移向量
        offset.copy(position).sub(scope.target);

        // 将偏移向量转换到"Y轴向上"的坐标空间
        offset.applyQuaternion(quat);

        // 将笛卡尔坐标转换为球面坐标（从Z轴绕Y轴的角度）
        spherical.setFromVector3(offset);
        
        // 根据当前控制模式应用不同的更新逻辑
        switch (scope.mode) {
          case KsgMode.Star:
            // 知识点层模式：应用旋转增量
            if (scope.enableDamping) {
              // 启用阻尼时，逐渐应用旋转变化
              spherical.theta += sphericalDelta.theta * scope.dampingFactor;
              spherical.phi += sphericalDelta.phi * scope.dampingFactor;
            } else {
              // 无阻尼时，直接应用旋转变化
              spherical.theta += sphericalDelta.theta;
              spherical.phi += sphericalDelta.phi;
            }
            break;
          case KsgMode.Domain:
            // 领域层模式：暂不处理球面坐标变化
            break;
        }

        // 限制极角phi在指定范围内，防止相机翻转
        spherical.phi = Math.max(
          scope.minPolarAngle,
          Math.min(scope.maxPolarAngle, spherical.phi)
        );
        // 确保球面坐标的安全性（处理边界情况）
        spherical.makeSafe();

        // 根据平移偏移量移动目标点
        switch (scope.mode) {
          case KsgMode.Star:
            // 知识点层模式：限制X和Z轴的平移，只允许Y轴平移
            panOffset.setX(0);  // 禁用X轴平移
            panOffset.setZ(0);  // 禁用Z轴平移
            if (scope.enableDamping === true) {
              // 启用阻尼时，逐渐应用平移
              scope.target.addScaledVector(panOffset, scope.dampingFactor);
            } else {
              // 无阻尼时，直接应用平移
              scope.target.add(panOffset);
            }
            // 限制目标点在Y轴上的范围
            scope.target.y = Math.max(scope.target.y, scope.yMinRange);
            scope.target.y = Math.min(
              scope.target.y,
              scope.yMaxRange - scope.yDelta
            );
            break;
          case KsgMode.Domain:
            // 领域层模式：在世界坐标系中处理平移
            if (scope.enableDamping === true) {
              // 更新根领域对象的世界矩阵
              scope.rootAreaObj.updateWorldMatrix(true, false);
              // 创建四维向量并应用阻尼
              //@ts-ignore
              const offset = new Vector4(...panOffset, 0)
                .multiplyScalar(scope.dampingFactor)  // 应用阻尼因子
                .applyMatrix4(scope.rootAreaObj.matrixWorld.clone().invert()); // 转换到局部坐标系
              // 将平移应用到子领域对象
              scope.subareas?.position.add(offset);
            } else {
              // 无阻尼时的处理
              scope.rootAreaObj.updateWorldMatrix(true, false);
              //@ts-ignore
              const offset = new Vector4(...panOffset, 0).applyMatrix4(
                scope.rootAreaObj.matrixWorld.clone().invert()
              );
              scope.subareas?.position.add(offset);
            }
            break;
        }

        // 处理缩放变化
        let zoomChanged = false;                                     // 缩放变化标记
        const prevRadius = spherical.radius;                        // 保存之前的半径
        spherical.radius = clampDistance(spherical.radius * scale); // 应用缩放并限制在范围内
        zoomChanged = prevRadius != spherical.radius;               // 检测是否发生缩放变化

        // 将球面坐标转换回笛卡尔坐标
        offset.setFromSpherical(spherical);

        // 将偏移向量转换回"相机向上向量为上"的坐标空间
        offset.applyQuaternion(quatInverse);

        // 根据控制模式应用最终的相机和对象位置
        switch (scope.mode) {
          case KsgMode.Star:
            // 知识点层模式：更新相机位置并让其看向目标
            position.copy(scope.target).add(offset);  // 设置相机位置为目标位置加偏移
            scope.object.lookAt(scope.target);        // 让相机朝向目标点
            break;
          case KsgMode.Domain:
            // 领域层模式：移动根领域对象而不是相机
            offset.setLength(cmap(dollyOffset, [-200, 200], [-1, 1])); // 将dolly偏移映射到[-1,1]范围并设置为偏移长度
            scope.rootAreaObj.position.add(offset.negate());          // 反向应用偏移到根对象
            // scope.object.lookAt(scope.target);  // 注释掉的相机朝向更新
            break;
        }

        // 应用阻尼效果或重置增量值
        if (scope.enableDamping === true) {
          // 启用阻尼时，逐渐减少各种操作的增量
          dollyOffset *= 1 - scope.dampingFactor;              // 缩放偏移阻尼
          sphericalDelta.theta *= 1 - scope.dampingFactor;     // 水平旋转阻尼
          sphericalDelta.phi *= 1 - scope.dampingFactor;       // 垂直旋转阻尼
          panOffset.multiplyScalar(1 - scope.dampingFactor);   // 平移阻尼
        } else {
          // 无阻尼时，重置所有增量值
          dollyOffset = 0;                    // 重置缩放偏移
          sphericalDelta.set(0, 0, 0);       // 重置球面坐标增量
          panOffset.set(0, 0, 0);            // 重置平移偏移
        }

        scale = 1; // 重置缩放因子

        // 更新条件检查：
        // 使用小角度近似：cos(x/2) = 1 - x^2 / 8
        // 当相机位移或旋转的平方大于EPS时需要更新

        // 根据控制模式限制相机Y轴位置
        switch (scope.mode) {
          case KsgMode.Star:
            // 知识点层模式：限制相机在Y轴上的位置范围
            scope.object.position.y = Math.min(
              Math.max(scope.object.position.y, scope.yMinRange), // 不小于最小值
              scope.yMaxRange                                      // 不大于最大值
            );
            break;
          case KsgMode.Domain:
            // 领域层模式：不限制相机Y轴位置
            break;
        }

        // 检查是否需要触发change事件
        if (
          zoomChanged ||  // 缩放发生变化
          lastPosition.distanceToSquared(scope.object.position) > EPS ||     // 相机位置变化超过阈值
          8 * (1 - lastQuaternion.dot(scope.object.quaternion)) > EPS ||     // 相机旋转变化超过阈值
          lastTargetPosition.distanceToSquared(scope.target) > EPS           // 目标位置变化超过阈值
        ) {
          // 触发change事件
          scope.dispatchEvent(_changeEvent);
          // 更新缓存的状态
          lastPosition.copy(scope.object.position);         // 缓存当前相机位置
          lastQuaternion.copy(scope.object.quaternion);     // 缓存当前相机旋转
          lastTargetPosition.copy(scope.target);            // 缓存当前目标位置
          return true;  // 返回true表示状态发生了变化
        }
        return false;   // 返回false表示状态没有变化
      };
    })();

    /**
     * 清理函数 - 移除所有事件监听器，释放资源
     * 在控制器不再使用时调用，防止内存泄漏
     */
    this.dispose = function () {
      // 清理自动旋转管理器
      if (scope.autoRotateManager) {
        scope.autoRotateManager.destroy();
      }

      // 移除DOM元素上的各种事件监听器
      scope.domElement.removeEventListener("contextmenu", onContextMenu);     // 移除右键菜单事件
      scope.domElement.removeEventListener("pointerdown", onPointerDown);     // 移除指针按下事件
      scope.domElement.removeEventListener("pointercancel", onPointerUp);     // 移除指针取消事件
      scope.domElement.removeEventListener("wheel", onMouseWheel);            // 移除滚轮事件
      scope.domElement.removeEventListener("pointermove", onPointerMove);     // 移除指针移动事件
      scope.domElement.removeEventListener("pointerup", onPointerUp);         // 移除指针抬起事件

      // 获取根文档节点（支持离屏canvas兼容性）
      const document = scope.domElement.getRootNode();
      // 移除键盘按键事件监听器
      document.removeEventListener("keydown", interceptControlDown, {
        capture: true,
      });

      // 如果存在键盘事件监听元素，移除相关事件并清空引用
      if (scope._domElementKeyEvents !== null) {
        scope._domElementKeyEvents.removeEventListener("keydown", onKeyDown);
        scope._domElementKeyEvents = null;
      }
    };

    /**
     * 模式切换函数 - 在不同控制模式间切换
     * @param mode 要切换到的控制模式
     */
    this.changeMode = function (mode) {
      scope.mode = mode;                    // 设置新的控制模式
      sphericalDelta.set(0, 0, 0);         // 重置球面坐标增量
      panOffset.set(0, 0, 0);              // 重置平移偏移
      dollyOffset = 0;                     // 重置缩放偏移
    };

    // === 内部状态和常量定义 ===
    const scope = this;  // 保存this引用，用于事件处理函数中访问控制器实例
    
    /**
     * 控制器内部状态枚举
     * 定义了各种操作状态，用于跟踪当前正在进行的操作类型
     */
    const STATE = {
      NONE: -1,                    // 无操作状态
      ROTATE: 0,                   // 旋转操作状态
      DOLLY: 1,                    // 缩放操作状态
      PAN: 2,                      // 平移操作状态
      TOUCH_ROTATE: 3,             // 触摸旋转状态
      TOUCH_PAN: 4,                // 触摸平移状态
      TOUCH_DOLLY_PAN: 5,          // 触摸缩放+平移状态
      TOUCH_DOLLY_ROTATE: 6,       // 触摸缩放+旋转状态
    };

    // === 内部状态变量 ===
    let state = STATE.NONE;        // 当前操作状态
    let scale = 1;                 // 缩放因子
    let controlActive = false;     // Control键是否被按下
    let dollyOffset = 0;           // 缩放偏移量
    const EPS = 0.000001;          // 极小值，用于浮点数比较

    // === 向量和几何计算变量 ===
    const panOffset = new Vector3();        // 平移偏移向量
    const rotateStart = new Vector2();      // 旋转开始位置
    const rotateEnd = new Vector2();        // 旋转结束位置
    const rotateDelta = new Vector2();      // 旋转增量
    const panStart = new Vector2();         // 平移开始位置
    const panEnd = new Vector2();           // 平移结束位置
    const panDelta = new Vector2();         // 平移增量
    const dollyStart = new Vector2();       // 缩放开始位置
    const dollyEnd = new Vector2();         // 缩放结束位置
    const dollyDelta = new Vector2();       // 缩放增量
    const dollyDirection = new Vector3();   // 缩放方向向量

    // === 球面坐标系统 ===
    const spherical = new Spherical();      // 当前球面坐标位置
    const sphericalDelta = new Spherical(); // 球面坐标增量

    // === 多点触控支持 ===
    const pointers: any[] = [];                              // 活动指针数组
    const pointerPositions: { [key: number]: Vector2 } = {}; // 指针位置映射表

    /**
     * 获取缩放比例
     * @param delta 鼠标滚轮或触摸的增量值
     * @returns 计算出的缩放比例
     */
    function getZoomScale(delta: number) {
      const normalizedDelta = Math.abs(delta * 0.01);  // 标准化增量值
      return Math.pow(0.95, scope.zoomSpeed * normalizedDelta);  // 使用指数函数计算缩放比例
    }

    /**
     * 向左旋转（水平旋转）
     * @param angle 旋转角度（弧度）
     */
    function rotateLeft(angle: number) {
      sphericalDelta.theta -= angle;  // 减少theta角度实现向左旋转
    }

    /**
     * 向上旋转（垂直旋转）
     * @param angle 旋转角度（弧度）
     */
    function rotateUp(angle: number) {
      sphericalDelta.phi -= angle;    // 减少phi角度实现向上旋转
    }

    /**
     * 向左平移函数（使用闭包模式优化性能）
     * 从相机的视角来看向左移动
     */
    const panLeft = (function () {
      const v = new Vector3();  // 复用的临时向量，避免频繁创建对象
      /**
       * 实际的向左平移函数
       * @param distance 平移距离
       * @param objectMatrix 对象的变换矩阵
       */
      return function panLeft(distance: number, objectMatrix: Matrix4) {
        v.setFromMatrixColumn(objectMatrix, 0); // 从变换矩阵的第0列获取X轴方向（右方向）
        v.multiplyScalar(-distance);            // 乘以负距离得到左方向
        panOffset.add(v);                       // 将左方向偏移添加到总平移偏移中
      };
    })();

    /**
     * 向上平移函数（使用闭包模式优化性能）
     * 从相机的视角来看向上移动
     */
    const panUp = (function () {
      const v = new Vector3();  // 复用的临时向量，避免频繁创建对象
      /**
       * 实际的向上平移函数
       * @param distance 平移距离
       * @param objectMatrix 对象的变换矩阵
       */
      return function panUp(distance: number, objectMatrix: Matrix4) {
        v.setFromMatrixColumn(objectMatrix, 1); // 从变换矩阵的第1列获取Y轴方向（上方向）
        v.multiplyScalar(distance);             // 乘以距离
        panOffset.add(v);                       // 将上方向偏移添加到总平移偏移中
      };
    })();

    /**
     * 主平移函数（使用闭包模式优化性能）
     * 处理屏幕坐标到3D世界坐标的转换
     * deltaX和deltaY是像素单位；右和下为正值
     */
    const pan = (function () {
      const offset = new Vector3();  // 复用的偏移向量
      /**
       * 实际的平移函数
       * @param deltaX 屏幕X方向的像素增量
       * @param deltaY 屏幕Y方向的像素增量
       */
      return function pan(deltaX: number, deltaY: number) {
        const element = scope.domElement;   // 获取DOM元素引用
        const position = scope.object.position;  // 获取相机位置

        // 根据控制模式计算不同的偏移基准
        switch (scope.mode) {
          case KsgMode.Star:
            // 知识点层模式：基于相机到目标的距离计算偏移
            offset.copy(position).sub(scope.target);
            break;
          case KsgMode.Domain:
            // 领域层模式：反转X、Y方向，基于相机到根对象的距离
            deltaX = -deltaX;
            deltaY = -deltaY;
            offset.copy(position).sub(scope.rootAreaObj.position);
            break;
        }

        let targetDistance = offset.length();  // 计算到目标的距离
        
        // 计算视野的一半，从中心到屏幕顶部的距离
        // 使用透视相机的fov（视野角度）来计算
        targetDistance *= Math.tan(((scope.object.fov / 2) * Math.PI) / 180.0);
        
        // 计算平移距离并应用
        // 只使用clientHeight来计算，这样纵横比不会影响平移速度
        panLeft(
          (2 * deltaX * targetDistance) / element.clientHeight,  // X方向平移距离
          scope.object.matrix  // 相机的变换矩阵
        );
        panUp(
          (2 * deltaY * targetDistance) / element.clientHeight,  // Y方向平移距离
          scope.object.matrix  // 相机的变换矩阵
        );
      };
    })();

    /**
     * 缩小操作（拉远相机）
     * @param dollyScale 缩放比例因子
     */
    function dollyOut(dollyScale: number) {
      scale /= dollyScale;  // 除以缩放因子实现缩小
    }

    /**
     * 放大操作（拉近相机）
     * @param dollyScale 缩放比例因子
     */
    function dollyIn(dollyScale: number) {
      scale *= dollyScale;  // 乘以缩放因子实现放大
    }

    /**
     * 限制距离在最小值和最大值之间
     * @param dist 待限制的距离值
     * @returns 限制后的距离值
     */
    function clampDistance(dist: number) {
      return Math.max(scope.minDistance, Math.min(scope.maxDistance, dist));
    }

    // === 鼠标事件处理函数 ===

    /**
     * 处理鼠标按下时的旋转操作初始化
     * @param event 鼠标事件对象
     */
    function handleMouseDownRotate(event: MouseEvent) {
      rotateStart.set(event.clientX, event.clientY);  // 记录旋转开始时的鼠标位置
    }

    /**
     * 处理鼠标按下时的缩放操作初始化
     * @param event 鼠标事件对象
     */
    function handleMouseDownDolly(event: MouseEvent) {
      dollyStart.set(event.clientX, event.clientY);   // 记录缩放开始时的鼠标位置
    }

    /**
     * 处理鼠标按下时的平移操作初始化
     * @param event 鼠标事件对象
     */
    function handleMouseDownPan(event: MouseEvent) {
      panStart.set(event.clientX, event.clientY);     // 记录平移开始时的鼠标位置
    }

    /**
     * 处理鼠标移动时的旋转操作
     * @param event 鼠标事件对象
     */
    function handleMouseMoveRotate(event: MouseEvent) {
      rotateEnd.set(event.clientX, event.clientY);    // 记录当前鼠标位置
      rotateDelta
        .subVectors(rotateEnd, rotateStart)           // 计算鼠标移动增量
        .multiplyScalar(scope.rotateSpeed);           // 应用旋转速度倍数
      
      const element = scope.domElement;               // 获取DOM元素引用
      
      // 计算水平旋转角度（注意：使用clientHeight而不是clientWidth）
      rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight);
      // 计算垂直旋转角度
      rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight);
      
      rotateStart.copy(rotateEnd);                    // 更新旋转起始位置为当前位置
      scope.update();                                 // 立即更新控制器状态
    }

    /**
     * 处理鼠标移动时的缩放操作
     * @param event 鼠标事件对象
     */
    function handleMouseMoveDolly(event: MouseEvent) {
      dollyEnd.set(event.clientX, event.clientY);     // 记录当前鼠标位置
      dollyDelta.subVectors(dollyEnd, dollyStart);    // 计算鼠标移动增量
      
      if (dollyDelta.y > 0) {
        // 鼠标向下移动，执行缩小操作
        dollyOut(getZoomScale(dollyDelta.y));
      } else if (dollyDelta.y < 0) {
        // 鼠标向上移动，执行放大操作
        dollyIn(getZoomScale(dollyDelta.y));
      }
      
      dollyOffset += dollyDelta.y;                    // 累加缩放偏移量
      dollyStart.copy(dollyEnd);                      // 更新缩放起始位置为当前位置
      scope.update();                                 // 立即更新控制器状态
    }

    /**
     * 处理鼠标移动时的平移操作
     * @param event 鼠标事件对象
     */
    function handleMouseMovePan(event: MouseEvent) {
      panEnd.set(event.clientX, event.clientY);       // 记录当前鼠标位置
      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed);  // 计算平移增量并应用速度倍数
      
      pan(panDelta.x, panDelta.y);                    // 执行平移操作
      panStart.copy(panEnd);                          // 更新平移起始位置为当前位置
      scope.update();                                 // 立即更新控制器状态
    }

    /**
     * 处理鼠标滚轮事件
     * @param event 自定义的滚轮事件对象
     */
    function handleMouseWheel(event: any) {
      if (event.deltaY < 0) {
        // 滚轮向上，执行放大操作
        dollyIn(getZoomScale(event.deltaY));
      } else if (event.deltaY > 0) {
        // 滚轮向下，执行缩小操作
        dollyOut(getZoomScale(event.deltaY));
      }
      dollyOffset += event.deltaY;                    // 累加缩放偏移量
      scope.update();                                 // 立即更新控制器状态
    }

    /**
     * 处理键盘按键事件
     * 支持箭头键进行平移和旋转操作
     * @param event 键盘事件对象
     */
    function handleKeyDown(event: {
      code: any;          // 按键代码
      ctrlKey: any;       // Ctrl键是否按下
      metaKey: any;       // Meta键（Cmd键）是否按下
      shiftKey: any;      // Shift键是否按下
      preventDefault: () => void;  // 阻止默认行为的方法
    }) {
      let needsUpdate = false;  // 是否需要更新控制器状态

      switch (event.code) {
        case scope.keys.UP:  // 上箭头键
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            // 如果按下修饰键，执行向上旋转
            rotateUp(
              (2 * Math.PI * scope.rotateSpeed) / scope.domElement.clientHeight
            );
          } else {
            // 否则执行向上平移
            pan(0, scope.keyPanSpeed);
          }
          needsUpdate = true;
          break;

        case scope.keys.BOTTOM:  // 下箭头键
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            // 如果按下修饰键，执行向下旋转
            rotateUp(
              (-2 * Math.PI * scope.rotateSpeed) / scope.domElement.clientHeight
            );
          } else {
            // 否则执行向下平移
            pan(0, -scope.keyPanSpeed);
          }
          needsUpdate = true;
          break;

        case scope.keys.LEFT:  // 左箭头键
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            // 如果按下修饰键，执行向左旋转
            rotateLeft(
              (2 * Math.PI * scope.rotateSpeed) / scope.domElement.clientHeight
            );
          } else {
            // 否则执行向左平移
            pan(scope.keyPanSpeed, 0);
          }
          needsUpdate = true;
          break;

        case scope.keys.RIGHT:  // 右箭头键
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            // 如果按下修饰键，执行向右旋转
            rotateLeft(
              (-2 * Math.PI * scope.rotateSpeed) / scope.domElement.clientHeight
            );
          } else {
            // 否则执行向右平移
            pan(-scope.keyPanSpeed, 0);
          }
          needsUpdate = true;
          break;
      }

      if (needsUpdate) {
        // 阻止浏览器的默认滚动行为
        event.preventDefault();
        // 更新控制器状态
        scope.update();
      }
    }

    // === 触摸事件处理函数 ===

    /**
     * 处理触摸开始时的旋转操作初始化
     * 支持单指和双指旋转
     * @param event 指针事件对象
     */
    function handleTouchStartRotate(event: PointerEvent) {
      if (pointers.length === 1) {
        // 单指触摸：直接使用触摸点位置
        rotateStart.set(event.pageX, event.pageY);
      } else {
        // 多指触摸：使用两个触摸点的中心位置
        const position = getSecondPointerPosition(event);
        const x = 0.5 * (event.pageX + position.x);  // 计算X轴中心点
        const y = 0.5 * (event.pageY + position.y);  // 计算Y轴中心点
        rotateStart.set(x, y);
      }
    }

    /**
     * 处理触摸开始时的平移操作初始化
     * 支持单指和双指平移
     * @param event 指针事件对象
     */
    function handleTouchStartPan(event: PointerEvent) {
      if (pointers.length === 1) {
        // 单指触摸：直接使用触摸点位置
        panStart.set(event.pageX, event.pageY);
      } else {
        // 多指触摸：使用两个触摸点的中心位置
        const position = getSecondPointerPosition(event);
        const x = 0.5 * (event.pageX + position.x);  // 计算X轴中心点
        const y = 0.5 * (event.pageY + position.y);  // 计算Y轴中心点
        panStart.set(x, y);
      }
    }

    /**
     * 处理触摸开始时的缩放操作初始化
     * 需要双指操作，计算两指间的距离
     * @param event 指针事件对象
     */
    function handleTouchStartDolly(event: PointerEvent) {
      const position = getSecondPointerPosition(event);  // 获取第二个触摸点位置
      
      // 计算两个触摸点之间的距离
      const dx = event.pageX - position.x;
      const dy = event.pageY - position.y;
      const distance = Math.sqrt(dx * dx + dy * dy);  // 欧几里得距离
      
      dollyStart.set(0, distance);  // 记录初始距离（X轴设为0，Y轴为距离）
    }

    /**
     * 处理触摸开始时的缩放+平移组合操作初始化
     * @param event 指针事件对象
     */
    function handleTouchStartDollyPan(event: any) {
      if (scope.enableZoom) handleTouchStartDolly(event);  // 如果启用缩放，初始化缩放操作
      if (scope.enablePan) handleTouchStartPan(event);     // 如果启用平移，初始化平移操作
    }

    /**
     * 处理触摸开始时的缩放+旋转组合操作初始化
     * @param event 指针事件对象
     */
    function handleTouchStartDollyRotate(event: any) {
      if (scope.enableZoom) handleTouchStartDolly(event);     // 如果启用缩放，初始化缩放操作
      if (scope.enableRotate) handleTouchStartRotate(event); // 如果启用旋转，初始化旋转操作
    }

    /**
     * 处理触摸移动时的旋转操作
     * 支持单指和双指旋转
     * @param event 指针事件对象
     */
    function handleTouchMoveRotate(event: PointerEvent) {
      if (pointers.length == 1) {
        // 单指旋转：直接使用当前触摸点位置
        rotateEnd.set(event.pageX, event.pageY);
      } else {
        // 多指旋转：使用两个触摸点的中心位置
        const position = getSecondPointerPosition(event);
        const x = 0.5 * (event.pageX + position.x);  // 计算X轴中心点
        const y = 0.5 * (event.pageY + position.y);  // 计算Y轴中心点
        rotateEnd.set(x, y);
      }

      // 计算旋转增量并应用旋转速度
      rotateDelta
        .subVectors(rotateEnd, rotateStart)         // 计算位置变化
        .multiplyScalar(scope.rotateSpeed);         // 应用旋转速度倍数

      const element = scope.domElement;             // 获取DOM元素引用

      // 计算并应用水平旋转（注意：使用clientHeight）
      rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight);
      // 计算并应用垂直旋转
      rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight);

      rotateStart.copy(rotateEnd);                  // 更新旋转起始位置
    }

    /**
     * 处理触摸移动时的平移操作
     * 支持单指和双指平移
     * @param event 指针事件对象
     */
    function handleTouchMovePan(event: PointerEvent) {
      if (pointers.length === 1) {
        // 单指平移：直接使用当前触摸点位置
        panEnd.set(event.pageX, event.pageY);
      } else {
        // 多指平移：使用两个触摸点的中心位置
        const position = getSecondPointerPosition(event);
        const x = 0.5 * (event.pageX + position.x);  // 计算X轴中心点
        const y = 0.5 * (event.pageY + position.y);  // 计算Y轴中心点
        panEnd.set(x, y);
      }

      // 计算平移增量并应用平移速度
      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed);

      pan(panDelta.x, panDelta.y);                   // 执行平移操作
      panStart.copy(panEnd);                         // 更新平移起始位置
    }

    /**
     * 处理触摸移动时的缩放操作
     * 需要双指操作，基于两指间距离的变化
     * @param event 指针事件对象
     */
    function handleTouchMoveDolly(event: PointerEvent) {
      const position = getSecondPointerPosition(event);  // 获取第二个触摸点位置

      // 计算当前两个触摸点之间的距离
      const dx = event.pageX - position.x;
      const dy = event.pageY - position.y;
      const distance = Math.sqrt(dx * dx + dy * dy);     // 欧几里得距离

      dollyEnd.set(0, distance);                         // 记录当前距离

      // 计算缩放增量：基于距离变化的比例
      dollyDelta.set(0, Math.pow(dollyEnd.y / dollyStart.y, scope.zoomSpeed));

      dollyOut(dollyDelta.y);                            // 执行缩放操作
      dollyStart.copy(dollyEnd);                         // 更新缩放起始距离

      // 计算两指中心点（可用于后续扩展功能）
      const centerX = (event.pageX + position.x) * 0.5;
      const centerY = (event.pageY + position.y) * 0.5;
    }

    /**
     * 处理触摸移动时的缩放+平移组合操作
     * @param event 指针事件对象
     */
    function handleTouchMoveDollyPan(event: PointerEvent) {
      if (scope.enableZoom) handleTouchMoveDolly(event);  // 如果启用缩放，处理缩放
      if (scope.enablePan) handleTouchMovePan(event);     // 如果启用平移，处理平移
    }

    /**
     * 处理触摸移动时的缩放+旋转组合操作
     * @param event 指针事件对象
     */
    function handleTouchMoveDollyRotate(event: PointerEvent) {
      if (scope.enableZoom) handleTouchMoveDolly(event);     // 如果启用缩放，处理缩放
      if (scope.enableRotate) handleTouchMoveRotate(event); // 如果启用旋转，处理旋转
    }

    // === 主要的指针事件处理函数 ===

    /**
     * 处理指针按下事件（统一处理鼠标和触摸）
     * 这是所有交互的入口点
     * @param event 指针事件对象
     */
    function onPointerDown(event: PointerEvent) {
      if (scope.enabled === false) return;  // 如果控制器被禁用，直接返回

      if (pointers.length === 0) {
        // 如果这是第一个指针，设置指针捕获并添加事件监听器
        scope.domElement.setPointerCapture(event.pointerId);                    // 捕获指针
        scope.domElement.addEventListener("pointermove", onPointerMove);        // 添加移动监听
        scope.domElement.addEventListener("pointerup", onPointerUp);            // 添加抬起监听
      }

      if (isTrackingPointer(event)) return;  // 如果已经在跟踪此指针，直接返回

      addPointer(event);  // 添加指针到跟踪列表

      // 根据指针类型分发事件
      if (event.pointerType === "touch") {
        onTouchStart(event);  // 触摸事件
      } else {
        onMouseDown(event);   // 鼠标事件
      }
    }

    /**
     * 处理指针移动事件（统一处理鼠标和触摸移动）
     * @param event 指针事件对象
     */
    function onPointerMove(event: PointerEvent) {
      if (scope.enabled === false) return;  // 如果控制器被禁用，直接返回

      changeControlsStatus();  // 更新控制状态（标记为正在控制中）

      // 根据指针类型分发事件
      if (event.pointerType === "touch") {
        onTouchMove(event);  // 触摸移动事件
      } else {
        onMouseMove(event);  // 鼠标移动事件
      }
    }

    /**
     * 修改控制状态
     * 设置控制器为活动状态，并在300毫秒后自动设置为非活动状态
     */
    function changeControlsStatus() {
      if (scope.controlsTimer) clearTimeout(scope.controlsTimer as number);  // 清除现有计时器
      scope.isControls = true;  // 设置为控制中状态
      scope.controlsTimer = setTimeout(() => {
        scope.isControls = false;  // 300毫秒后设置为非控制状态
      }, 300);
    }

    /**
     * 处理指针抬起事件（统一处理鼠标和触摸结束）
     * @param event 指针事件对象
     */
    function onPointerUp(event: PointerEvent) {
      removePointer(event);  // 从跟踪列表中移除指针

      switch (pointers.length) {
        case 0:
          // 如果没有活动指针了，清理事件监听器并重置状态
          scope.domElement.releasePointerCapture(event.pointerId);            // 释放指针捕获
          scope.domElement.removeEventListener("pointermove", onPointerMove); // 移除移动监听
          scope.domElement.removeEventListener("pointerup", onPointerUp);     // 移除抬起监听
          scope.dispatchEvent(_endEvent);  // 触发结束事件
          state = STATE.NONE;              // 重置状态为无操作
          break;
        case 1:
          // 如果还有一个活动指针，重新初始化单指操作
          const pointerId = pointers[0];
          const position = pointerPositions[pointerId];
          // 创建最小的占位事件 - 允许在指针抬起时进行状态修正
          onTouchStart({
            pointerId: pointerId,
            pageX: position.x,
            pageY: position.y,
          } as PointerEvent);
          break;
      }
    }

    /**
     * 处理鼠标按下事件
     * 根据鼠标按键和修饰键确定操作类型
     * @param event 鼠标事件对象
     */
    function onMouseDown(event: MouseEvent) {
      let mouseAction;  // 鼠标操作类型

      // 根据鼠标按键确定基础操作
      switch (event.button) {
        case 0:  // 左键
          mouseAction = scope.mouseButtons.LEFT;
          break;
        case 1:  // 中键（滚轮）
          mouseAction = scope.mouseButtons.MIDDLE;
          break;
        case 2:  // 右键
          mouseAction = scope.mouseButtons.RIGHT;
          break;
        default:
          mouseAction = -1;  // 未知按键
      }

      // 根据鼠标操作类型和修饰键确定最终操作
      switch (mouseAction) {
        case MOUSE.DOLLY:  // 缩放操作（通常是中键）
          if (scope.enableZoom === false) return;  // 如果禁用缩放，直接返回
          handleMouseDownDolly(event);             // 初始化缩放操作
          state = STATE.DOLLY;                     // 设置状态为缩放
          break;

        case MOUSE.ROTATE:  // 旋转操作（通常是左键）
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            // 如果按下修饰键，执行平移而不是旋转
            if (scope.enablePan === false) return;  // 如果禁用平移，直接返回
            handleMouseDownPan(event);              // 初始化平移操作
            state = STATE.PAN;                      // 设置状态为平移
          } else {
            // 正常旋转操作
            if (scope.enableRotate === false) return;  // 如果禁用旋转，直接返回
            handleMouseDownRotate(event);              // 初始化旋转操作
            state = STATE.ROTATE;                      // 设置状态为旋转
          }
          break;

        case MOUSE.PAN:  // 平移操作（通常是右键）
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            // 如果按下修饰键，执行旋转而不是平移
            if (scope.enableRotate === false) return;  // 如果禁用旋转，直接返回
            handleMouseDownRotate(event);              // 初始化旋转操作
            state = STATE.ROTATE;                      // 设置状态为旋转
          } else {
            // 正常平移操作
            if (scope.enablePan === false) return;  // 如果禁用平移，直接返回
            handleMouseDownPan(event);              // 初始化平移操作
            state = STATE.PAN;                      // 设置状态为平移
          }
          break;

        default:
          state = STATE.NONE;  // 未知操作，设置为无状态
      }

      if (state !== STATE.NONE) {
        scope.dispatchEvent(_startEvent);  // 如果开始了操作，触发开始事件
      }
    }

    /**
     * 处理鼠标移动事件
     * 根据当前状态执行相应的操作
     * @param event 鼠标事件对象
     */
    function onMouseMove(event: MouseEvent) {
      switch (state) {
        case STATE.ROTATE:  // 旋转状态
          if (scope.enableRotate === false) return;  // 如果禁用旋转，直接返回
          handleMouseMoveRotate(event);              // 处理旋转移动
          break;

        case STATE.DOLLY:  // 缩放状态
          if (scope.enableZoom === false) return;  // 如果禁用缩放，直接返回
          handleMouseMoveDolly(event);             // 处理缩放移动
          break;

        case STATE.PAN:  // 平移状态
          if (scope.enablePan === false) return;  // 如果禁用平移，直接返回
          handleMouseMovePan(event);              // 处理平移移动
          break;
      }
    }

    /**
     * 处理鼠标滚轮事件
     * 用于缩放操作
     * @param event 滚轮事件对象
     */
    function onMouseWheel(event: WheelEvent) {
      // 检查各种条件：控制器启用、缩放启用、当前无其他操作
      if (
        scope.enabled === false ||
        scope.enableZoom === false ||
        state !== STATE.NONE
      )
        return;

      event.preventDefault();                        // 阻止默认滚动行为
      scope.dispatchEvent(_startEvent);             // 触发开始事件
      handleMouseWheel(customWheelEvent(event));    // 处理自定义滚轮事件
      scope.dispatchEvent(_endEvent);               // 触发结束事件
    }

    /**
     * 自定义滚轮事件处理
     * 标准化不同浏览器和设备的滚轮行为
     * @param event 原始滚轮事件
     * @returns 标准化的滚轮事件对象
     */
    function customWheelEvent(event: WheelEvent) {
      const mode = event.deltaMode;  // 获取增量模式
      
      // 创建最小化的滚轮事件对象，适应缩放需求
      const newEvent = {
        clientX: event.clientX,    // X坐标
        clientY: event.clientY,    // Y坐标
        deltaY: event.deltaY,      // Y方向增量
      };

      // 根据不同的增量模式调整增量值
      switch (mode) {
        case WheelEvent.DOM_DELTA_LINE: // 行模式
          newEvent.deltaY *= 16;        // 放大16倍
          break;
        case WheelEvent.DOM_DELTA_PAGE: // 页面模式
          newEvent.deltaY *= 100;       // 放大100倍
          break;
        // DOM_DELTA_PIXEL 模式不需要调整
      }

      // 检测是否由捏合手势触发（触控板双指缩放）
      if (event.ctrlKey && !controlActive) {
        newEvent.deltaY *= 10;  // 捏合手势增强敏感度
      }

      return newEvent;
    }

    /**
     * 拦截Control键按下事件
     * 用于区分捏合手势和普通滚轮
     * @param event 键盘事件
     */
    function interceptControlDown(event: Event) {
      const e = event as KeyboardEvent;
      if (e.key === "Control") {
        controlActive = true;  // 标记Control键激活
        
        // 获取根文档节点（支持离屏canvas兼容性）
        const document = scope.domElement.getRootNode();
        // 添加Control键抬起监听器
        document.addEventListener("keyup", interceptControlUp, {
          passive: true,
          capture: true,
        });
      }
    }

    /**
     * 拦截Control键抬起事件
     * @param event 键盘事件
     */
    function interceptControlUp(event: Event) {
      const e = event as KeyboardEvent;
      if (e.key === "Control") {
        controlActive = false;  // 取消Control键激活状态
        
        // 获取根文档节点（支持离屏canvas兼容性）
        const document = scope.domElement.getRootNode();
        // 移除Control键抬起监听器
        document.removeEventListener("keyup", interceptControlUp, {
          capture: true,
        });
      }
    }

    /**
     * 键盘按键事件处理
     * @param event 键盘事件对象
     */
    function onKeyDown(event: KeyboardEvent) {
      // 检查控制器是否启用以及平移功能是否启用
      if (scope.enabled === false || scope.enablePan === false) return;
      handleKeyDown(event);  // 调用键盘处理函数
    }

    /**
     * 处理触摸开始事件
     * 根据触摸点数量确定操作类型
     * @param event 指针事件对象
     */
    function onTouchStart(event: PointerEvent) {
      trackPointer(event);  // 跟踪指针位置

      // 根据触摸点数量确定操作类型
      switch (pointers.length) {
        case 1:  // 单指触摸
          switch (scope.touches.ONE) {
            case TOUCH.ROTATE:  // 单指旋转模式
              if (scope.enableRotate === false) return;  // 检查旋转是否启用
              handleTouchStartRotate(event);             // 初始化旋转操作
              state = STATE.TOUCH_ROTATE;                // 设置状态为触摸旋转
              break;

            case TOUCH.PAN:  // 单指平移模式
              if (scope.enablePan === false) return;  // 检查平移是否启用
              handleTouchStartPan(event);             // 初始化平移操作
              state = STATE.TOUCH_PAN;                // 设置状态为触摸平移
              break;

            default:
              state = STATE.NONE;  // 未定义的单指操作
          }
          break;

        case 2:  // 双指触摸
          switch (scope.touches.TWO) {
            case TOUCH.DOLLY_PAN:  // 双指缩放+平移模式
              // 检查缩放和平移是否都被禁用
              if (scope.enableZoom === false && scope.enablePan === false)
                return;
              handleTouchStartDollyPan(event);     // 初始化缩放+平移操作
              state = STATE.TOUCH_DOLLY_PAN;       // 设置状态为触摸缩放+平移
              break;

            case TOUCH.DOLLY_ROTATE:  // 双指缩放+旋转模式
              // 检查缩放和旋转是否都被禁用
              if (scope.enableZoom === false && scope.enableRotate === false)
                return;
              handleTouchStartDollyRotate(event);  // 初始化缩放+旋转操作
              state = STATE.TOUCH_DOLLY_ROTATE;    // 设置状态为触摸缩放+旋转
              break;

            default:
              state = STATE.NONE;  // 未定义的双指操作
          }
          break;

        default:
          state = STATE.NONE;  // 三指及以上，不支持
      }

      if (state !== STATE.NONE) {
        scope.dispatchEvent(_startEvent);  // 如果开始了操作，触发开始事件
      }
    }

    /**
     * 处理触摸移动事件
     * 根据当前状态执行相应的操作
     * @param event 指针事件对象
     */
    function onTouchMove(event: PointerEvent) {
      trackPointer(event);  // 更新指针位置跟踪

      switch (state) {
        case STATE.TOUCH_ROTATE:  // 触摸旋转状态
          if (scope.enableRotate === false) return;  // 检查旋转是否启用
          handleTouchMoveRotate(event);              // 处理旋转移动
          scope.update();                            // 更新控制器状态
          break;

        case STATE.TOUCH_PAN:  // 触摸平移状态
          if (scope.enablePan === false) return;  // 检查平移是否启用
          handleTouchMovePan(event);              // 处理平移移动
          scope.update();                         // 更新控制器状态
          break;

        case STATE.TOUCH_DOLLY_PAN:  // 触摸缩放+平移状态
          // 检查缩放和平移是否都被禁用
          if (scope.enableZoom === false && scope.enablePan === false) return;
          handleTouchMoveDollyPan(event);  // 处理缩放+平移移动
          scope.update();                  // 更新控制器状态
          break;

        case STATE.TOUCH_DOLLY_ROTATE:  // 触摸缩放+旋转状态
          // 检查缩放和旋转是否都被禁用
          if (scope.enableZoom === false && scope.enableRotate === false)
            return;
          handleTouchMoveDollyRotate(event);  // 处理缩放+旋转移动
          scope.update();                     // 更新控制器状态
          break;

        default:
          state = STATE.NONE;  // 未知状态，重置为无操作
      }
    }

    /**
     * 处理右键菜单事件
     * 阻止默认的右键菜单显示
     * @param event 鼠标事件对象
     */
    function onContextMenu(event: MouseEvent) {
      if (scope.enabled === false) return;  // 如果控制器被禁用，直接返回
      event.preventDefault();               // 阻止默认的右键菜单
    }

    // === 指针管理辅助函数 ===

    /**
     * 添加指针到跟踪列表
     * @param event 指针事件对象
     */
    function addPointer(event: PointerEvent) {
      pointers.push(event.pointerId);  // 将指针ID添加到活动指针列表
    }

    /**
     * 从跟踪列表中移除指针
     * @param event 指针事件对象
     */
    function removePointer(event: PointerEvent) {
      delete pointerPositions[event.pointerId];  // 从位置映射中删除指针
      
      // 从活动指针数组中移除指针ID
      for (let i = 0; i < pointers.length; i++) {
        if (pointers[i] == event.pointerId) {
          pointers.splice(i, 1);  // 移除找到的指针ID
          return;
        }
      }
    }

    /**
     * 检查是否正在跟踪指定的指针
     * @param event 指针事件对象
     * @returns boolean 如果正在跟踪返回true
     */
    function isTrackingPointer(event: PointerEvent) {
      for (let i = 0; i < pointers.length; i++) {
        if (pointers[i] == event.pointerId) return true;  // 找到匹配的指针ID
      }
      return false;  // 未找到匹配的指针ID
    }

    /**
     * 跟踪指针位置
     * 更新或创建指针位置记录
     * @param event 指针事件对象
     */
    function trackPointer(event: PointerEvent) {
      let position = pointerPositions[event.pointerId];  // 获取现有位置记录
      
      if (position === undefined) {
        // 如果没有位置记录，创建新的
        position = new Vector2();
        pointerPositions[event.pointerId] = position;
      }
      
      position.set(event.pageX, event.pageY);  // 更新位置坐标
    }

    /**
     * 获取第二个指针的位置
     * 用于多点触控操作
     * @param event 当前指针事件对象
     * @returns Vector2 第二个指针的位置
     */
    function getSecondPointerPosition(event: PointerEvent) {
      // 确定第二个指针的ID（不是当前事件的指针）
      const pointerId =
        event.pointerId === pointers[0] ? pointers[1] : pointers[0];
      return pointerPositions[pointerId];  // 返回第二个指针的位置
    }

    // === 事件监听器初始化 ===
    
    // 为DOM元素添加各种事件监听器
    scope.domElement.addEventListener("contextmenu", onContextMenu);    // 右键菜单事件
    scope.domElement.addEventListener("pointerdown", onPointerDown);    // 指针按下事件
    scope.domElement.addEventListener("pointercancel", onPointerUp);    // 指针取消事件（当作抬起处理）
    scope.domElement.addEventListener("wheel", onMouseWheel, {          // 滚轮事件
      passive: false,  // 设置为非被动模式，允许preventDefault
    });
    
    // 获取根文档节点（支持离屏canvas兼容性）
    const document = scope.domElement.getRootNode();
    // 添加Control键监听器，用于检测捏合手势
    document.addEventListener("keydown", interceptControlDown, {
      passive: true,   // 被动模式，不会调用preventDefault
      capture: true,   // 捕获阶段处理，确保能够拦截到事件
    });
  }
  /**
   * 自动旋转更新函数
   * 若要开启自动旋转功能，请设置autoRotate为true
   * 此方法应在渲染帧中调用，实现相机绕目标点的自动旋转
   *
   * @param deltaTime 时间增量（秒），用于计算旋转角度
   */
  autoRotateUpdate(deltaTime: number) {
    // 检查是否启用了自动旋转功能
    if (this.autoRotate) {
      // 计算相机相对于目标点的偏移向量
      const offset1 = this.object.position.clone().sub(this.target);

      // 根据自动旋转速度和时间增量计算旋转角度
      const angle = this.autoRotateSpeed * deltaTime!;

      // 创建绕Y轴旋转的旋转矩阵
      const rotationMatrix = new Matrix4().makeRotationY(angle);

      // 将偏移向量应用旋转变换
      offset1.applyMatrix4(rotationMatrix);

      // 更新相机位置：目标位置 + 旋转后的偏移向量
      this.object.position.copy(this.target).add(offset1);

      // 让相机始终朝向目标点
      this.object.lookAt(this.target);

      // 在自动旋转时显示最近节点的标签
      this.updateClosestPointLabel();
    }
  }

  /**
   * 更新最靠近相机的节点标签显示
   * 在自动旋转时调用，动态显示距离相机最近的节点标签
   * @private
   */
  private updateClosestPointLabel() {
    // 这个方法将在外部实现，避免循环依赖
    // 具体实现在 hooks/useRendererFrame.ts 中
  }
}

export { KsgMode, KsgControls };
