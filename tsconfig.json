{
  "compilerOptions": {
    "declaration": true, // 生成类型声明文件
    "emitDeclarationOnly": true, // 仅生成类型声明文件，不生成编译后的 JavaScript
    "outDir": "package/types", // 输出目录
    "target": "esnext",
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "allowImportingTsExtensions": true,
    "jsx": "preserve",
    "sourceMap": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
