import TWEEN from "@tweenjs/tween.js";
import type { Point } from "../types";
import KsgPoint from "../core/KsgPoints";
import KsgLine from "../core/KsgLine";
import { KsgLabel } from "../core/KsgLabel";
import { FocusCrust } from "../core/focusCrust";
import { KsgControls } from "../core/KsgControls";
import { Vector3 } from "three";
/** 发生变化的节点*/
type ModifyPoint = {
  /*更新前 */
  old: Point;
  /**更新后 */
  new?: Point;
};
/**
 * @param {KsgPoint} pointsMesh 节点对象
 * @param {ModifyPoint} modifyPoints 发生变化的节点
 * @param {number} duration 动画持续时间
 */
export function updatePointPositionAnimation(
  pointsMesh: KsgPoint,
  line: KsgLine,
  focusCrust: FocusCrust,
  label: KsgLabel,
  modifyPoints: ModifyPoint,
  focusChildren: Point[],
  duration: number = 600
): Promise<Point> {
  const [x0, y0, z0] = modifyPoints.old.coordinate;
  const [x1, y1, z1] = modifyPoints.new!.coordinate;
  const point = focusChildren.find((point) => point.id === modifyPoints.old.id);
  return new Promise((resolve) => {
    new TWEEN.Tween({ x: x0, y: y0, z: z0 })
      .to(
        {
          x: x1,
          y: y1,
          z: z1,
        },
        duration
      )
      .easing(TWEEN.Easing.Sinusoidal.Out)
      .start()
      .onComplete(() => resolve(modifyPoints.new!))
      .onUpdate(({ x, y, z }) => {
        pointsMesh.updatePosition([modifyPoints.old.index!], [x, y, z]);
        if (point) {
          line.updateEndPosition(point.endPointsIndex!, [x, y, z]);
        }
        if (pointsMesh.focusIndex === modifyPoints.old.index) {
          line.updateFocusPointPosition([x, y, z]);
          // 更新聚焦节点相关
          focusCrust.updatePosition([x, y, z]);
          label.updatePosition([x, y, z]);
        }
      });
  });
}
