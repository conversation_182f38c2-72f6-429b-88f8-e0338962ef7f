import type { PointData, Point } from "../types";
import FrameScheduler from "../utils/FrameScheduler";
export declare const frameScheduler: FrameScheduler;
/** 发生变化的节点*/
export type ModifyPoint = {
    old: Point;
    /**更新后 */
    new?: Point;
};
/**diff数据 */
export type DiffData = {
    /**最新数据 */
    newPoints: Point[];
    /**更新的数据 */
    updatePoints: Map<string, ModifyPoint>;
};
/**
 * DAG空间坐标计算
 */
export default class KsgGraph {
    pointsData: Map<string, Point>;
    idLevelMap: {
        [level: number]: string[];
    };
    /**加载更多diff数据*/
    diffData: DiffData;
    /**
     * @param pointsData 节点数据
     * @param levelSpace 层级间隔
     * @param pointSpace 点间隔
     */
    constructor(pointsData: PointData[]);
    /**
     * @param pointsData 节点数据
     */
    compute(pointsData: PointData[]): void;
    /**
     * 加载更多数据
     * @param pointsData 节点数据
     */
    loadMore(pointsData: PointData[]): Promise<DiffData>;
    /**
     * 把数据维护到pointsDataMap中
     * 父关系转为子关系
     * @param pointsData 节点数据
     */
    private build;
    /**
     * 计算层级
     */
    computeLevel(points: Map<string, Point>): void;
    /**
     * 计算每层节点的位置
     */
    computePointPosition(levelHeight?: number, pointSpace?: number): void;
    /**
     * 是否发生位置辩护1
     */
    private isPositionChange;
    /**
     * 根据id获取Point
     */
    getPointById(id: string): Point | null;
    /**
     * 获取当前图的最大层级
     * 从0层起
     */
    getMaxLevel(): number;
}
