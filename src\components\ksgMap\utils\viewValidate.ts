/**
 * 检测坐标是否在可视区域内
 * 用于判断某个点的坐标是否在指定的视图区域范围内
 * 
 * @param view 视图区域的边界定义
 * @param view.minX 视图区域的最小X坐标
 * @param view.maxX 视图区域的最大X坐标
 * @param view.minY 视图区域的最小Y坐标
 * @param view.maxY 视图区域的最大Y坐标
 * @param dnc 待检测的坐标位置
 * @param dnc.x 待检测点的X坐标
 * @param dnc.y 待检测点的Y坐标
 * @returns 如果坐标在可视区域内返回true，否则返回false
 */
export default function innerViewValidate(
  view: { minX: number; maxX: number; minY: number; maxY: number },
  dnc: { x: number; y: number }
): boolean {
  // 将相对坐标转换为绝对坐标
  dnc.x += view.minX;
  dnc.y += view.minY;
  
  // 检查是否超出视图边界
  if (
    dnc.x < view.minX ||
    dnc.x > view.maxX ||
    dnc.y < view.minY ||
    dnc.y > view.maxY
  ) {
    return false;
  }
  return true;
}
