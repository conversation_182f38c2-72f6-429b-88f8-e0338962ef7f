/**
 * 旋转标签管理器配置选项
 */
export interface RotateLabelManagerOptions {
  /** 标签切换的防抖延迟时间（毫秒），默认200ms */
  debounceDelay?: number;
  /** 最大检测距离，超过此距离的节点不会显示标签，默认100 */
  maxDistance?: number;
  /** 是否启用调试日志，默认false */
  debug?: boolean;
  /** 标签偏移量，默认{x: 15, y: 15} */
  labelOffset?: { x: number; y: number };
}

/**
 * 旋转标签管理器类
 * 负责在自动旋转时管理最近节点的标签显示
 */
export declare class RotateLabelManager {
  /**
   * 构造函数
   * @param options 配置选项
   */
  constructor(options?: RotateLabelManagerOptions);

  /**
   * 激活旋转标签管理器
   * 开始监控最近节点并显示标签
   */
  activate(): void;

  /**
   * 停用旋转标签管理器
   * 隐藏当前标签并停止监控
   */
  deactivate(): void;

  /**
   * 更新函数 - 在渲染循环中调用
   * 计算最近节点并更新标签显示
   */
  update(): void;

  /**
   * 销毁管理器
   * 清理所有资源
   */
  destroy(): void;

  /**
   * 获取当前激活状态
   */
  get isActive(): boolean;
}
