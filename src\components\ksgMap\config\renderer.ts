import * as THREE from "three";
import ctx from "../ctx";
import type { RendererConfig } from "../types/index";

/**
 * WebGL渲染器配置函数 - 创建和配置Three.js的WebGL渲染器
 *
 * WebGLRenderer是Three.js的核心渲染器，负责将3D场景渲染到HTML5 Canvas元素上
 * 它使用WebGL API来实现硬件加速的3D图形渲染，提供高性能的图形处理能力
 *
 * 主要功能：
 * - 将3D场景和相机的内容渲染到2D画布上
 * - 处理光照、材质、纹理等视觉效果
 * - 支持抗锯齿、阴影、后处理等高级渲染特性
 *
 * @param config 渲染器配置参数对象
 * @returns 返回包含渲染器实例和DOM元素的对象
 */
export default function useRenderer(
  config: RendererConfig
) {
  // 创建WebGL渲染器实例
  const renderer = new THREE.WebGLRenderer({
    // 抗锯齿设置 - 消除锯齿边缘，提供更平滑的视觉效果
    // 开启抗锯齿会消耗更多GPU资源，但能显著提升渲染质量
    antialias: true,

    // 展开用户自定义的WebGL渲染器配置
    // 允许用户覆盖默认设置或添加额外的WebGL选项
    ...(config?.webGLRenderer ?? {}),
  });

  // 设置渲染器的输出尺寸
  // 这决定了最终渲染图像的分辨率
  // width: 画布宽度（像素）
  // height: 画布高度（像素）
  renderer.setSize(config.width, config.height);

  // 设置设备像素比 - 处理高DPI显示器
  // window.devicePixelRatio 获取当前设备的像素密度比
  // 在Retina屏幕等高DPI设备上，这个值通常大于1
  // 正确设置像素比可以确保在高分辨率屏幕上显示清晰
  renderer.setPixelRatio(window.devicePixelRatio);

  // 将渲染器实例保存到全局上下文中
  // 这样其他模块可以访问和使用同一个渲染器实例
  ctx.renderer = renderer;

  // 返回渲染器相关对象
  return {
    // 渲染器实例 - 用于执行渲染操作
    renderer,

    // 渲染器的DOM元素 - 实际的Canvas元素
    // 这个Canvas元素需要被添加到HTML页面中才能显示渲染结果
    rendererDom: renderer.domElement,
  };
}
