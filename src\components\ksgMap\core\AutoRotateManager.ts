/**
 * 自动旋转管理器 - AutoRotateManager.ts
 * 
 * 职责：
 * 1. 管理自动旋转的启动和停止
 * 2. 监听用户交互事件，自动停止旋转
 * 3. 在用户停止操作后延迟重新启动自动旋转
 * 4. 提供统一的自动旋转状态管理接口
 * 
 * 特性：
 * - 支持初始化时自动启动
 * - 智能检测用户交互（鼠标、触摸、键盘）
 * - 可配置的延迟重启时间
 * - 防抖机制避免频繁启停
 * - 完整的生命周期管理
 */

import type { KsgControls } from './KsgControls';

/**
 * 自动旋转管理器配置选项
 */
export interface AutoRotateManagerOptions {
  /** 用户停止操作后多少毫秒重新启动自动旋转，默认5000ms */
  restartDelay?: number;
  /** 是否在初始化时立即启动自动旋转，默认true */
  autoStart?: boolean;
  /** 自动旋转速度，默认2.0 */
  rotateSpeed?: number;
  /** 是否启用调试日志，默认false */
  debug?: boolean;
}

/**
 * 自动旋转管理器类
 * 负责管理KsgControls的自动旋转功能
 */
export class AutoRotateManager {
  /** 关联的控制器实例 */
  private controls: KsgControls;
  /** 重启延迟时间（毫秒） */
  private restartDelay: number;
  /** 是否启用调试日志 */
  private debug: boolean;
  /** 延迟重启的定时器 */
  private restartTimer: number | NodeJS.Timeout | null = null;
  /** 是否已初始化 */
  private initialized = false;
  /** 是否已销毁 */
  private destroyed = false;

  /**
   * 构造函数
   * @param controls KsgControls实例
   * @param options 配置选项
   */
  constructor(controls: KsgControls, options: AutoRotateManagerOptions = {}) {
    this.controls = controls;
    this.restartDelay = options.restartDelay ?? 5000;
    this.debug = options.debug ?? false;

    // 设置自动旋转速度
    if (options.rotateSpeed !== undefined) {
      this.controls.autoRotateSpeed = options.rotateSpeed;
    }

    // 如果启用自动启动，则初始化时启动自动旋转
    if (options.autoStart !== false) {
      this.start();
    }

    this.log('AutoRotateManager initialized', { options });
  }

  /**
   * 启动自动旋转
   * 立即启用自动旋转并开始监听用户交互
   */
  start(): void {
    if (this.destroyed) {
      this.log('Cannot start: manager is destroyed');
      return;
    }

    this.controls.autoRotate = true;
    this.setupEventListeners();
    this.initialized = true;
    this.log('Auto rotate started');
  }

  /**
   * 停止自动旋转
   * 立即停止自动旋转并清除所有定时器
   */
  stop(): void {
    this.controls.autoRotate = false;
    this.clearRestartTimer();
    this.log('Auto rotate stopped');
  }

  /**
   * 暂停自动旋转并启动重启定时器
   * 用户交互时调用，会在指定延迟后自动重启
   */
  pause(): void {
    if (this.destroyed || !this.initialized) return;

    this.controls.autoRotate = false;
    this.scheduleRestart();
    this.log('Auto rotate paused, will restart in', this.restartDelay, 'ms');
  }

  /**
   * 重置重启定时器
   * 用户持续交互时调用，重新计算延迟时间
   */
  resetTimer(): void {
    if (this.destroyed || !this.initialized) return;

    this.clearRestartTimer();
    this.scheduleRestart();
    this.log('Restart timer reset');
  }

  /**
   * 销毁管理器
   * 清理所有资源和事件监听器
   */
  destroy(): void {
    this.stop();
    this.removeEventListeners();
    this.destroyed = true;
    this.log('AutoRotateManager destroyed');
  }

  /**
   * 获取当前自动旋转状态
   */
  get isActive(): boolean {
    return this.controls.autoRotate;
  }

  /**
   * 设置自动旋转速度
   */
  setSpeed(speed: number): void {
    this.controls.autoRotateSpeed = speed;
    this.log('Auto rotate speed set to', speed);
  }

  /**
   * 安排重启自动旋转
   * @private
   */
  private scheduleRestart(): void {
    this.clearRestartTimer();
    this.restartTimer = setTimeout(() => {
      if (!this.destroyed && this.initialized) {
        this.controls.autoRotate = true;
        this.log('Auto rotate restarted after delay');
      }
      this.restartTimer = null;
    }, this.restartDelay);
  }

  /**
   * 清除重启定时器
   * @private
   */
  private clearRestartTimer(): void {
    if (this.restartTimer) {
      clearTimeout(this.restartTimer as number);
      this.restartTimer = null;
    }
  }

  /**
   * 设置事件监听器
   * 监听控制器的start事件来检测用户交互
   * @private
   */
  private setupEventListeners(): void {
    // 监听控制器的start事件（用户开始交互）
    this.controls.addEventListener('start', this.handleUserInteraction);
  }

  /**
   * 移除事件监听器
   * @private
   */
  private removeEventListeners(): void {
    this.controls.removeEventListener('start', this.handleUserInteraction);
  }

  /**
   * 处理用户交互事件
   * @private
   */
  private handleUserInteraction = (): void => {
    if (this.controls.autoRotate) {
      this.pause();
    } else {
      this.resetTimer();
    }
  };

  /**
   * 调试日志输出
   * @private
   */
  private log(...args: any[]): void {
    if (this.debug) {
      console.log('[AutoRotateManager]', ...args);
    }
  }
}
