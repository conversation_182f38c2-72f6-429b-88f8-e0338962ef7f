// import { PointData, Graph } from "../types";
// /**
//  * 计算graph
//  */
// const worker = new Worker(new URL("./worker.ts", import.meta.url), {
//   type: "module",
// });
// export function calculateGraph(
//   pointsData: PointData[],
//   config: {
//     maxLevel: number;
//     levelSpace: number;
//     pointSpace: number;
//   }
// ): Promise<Graph> {
//   let graph: Graph | null = new Graph([], Infinity, Infinity, Infinity);
//   return new Promise((resolve) => {
//     worker.postMessage({
//       pointsData,
//       ...config,
//     });
//     worker.onmessage = (e: MessageEvent) => {
//       resolve(graph!.directInit(e.data.graph));
//       graph = null;
//     };
//   });
// }
