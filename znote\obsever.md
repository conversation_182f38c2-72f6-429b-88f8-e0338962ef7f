const observer = new MutationObserver(() => {
  let scripts = Array.prototype.slice.call(
    document.body.getElementsByTagName("script")
  );
  scripts.forEach(function (script) {
    if (!script.type || !script.type.match(/math\/tex/i)) {
      return -1;
    }
    const display = script.type.match(/mode\s*=\s*display(;|\s|\n|$)/) != null;

    const katexElement = document.createElement(display ? "div" : "span");
    katexElement.setAttribute(
      "class",
      display ? "equation" : "inline-equation"
    );

    // 使用安全的entities库进行HTML实体解码,避免xss攻击
    const decodedText = decodeHTML(script.text);
    katexElement.setAttribute("latexCode", script.text);

    try {
      // 预处理公式文本，将 align 环境替换为 align* 以禁用自动编号
      let processedText = decodedText.replace(/\s+/g, " ");
      processedText = processedText.replace(
        /\\begin\{align\}/g,
        "\\begin{align*}"
      );
      processedText = processedText.replace(/\\end\{align\}/g, "\\end{align*}");

      const htmlString = katex.renderToString(processedText, {
        displayMode: display,
        throwOnError: false,
        output: "html",
      });

      katexElement.innerHTML = htmlString;
    } catch (err) {
      //console.error(err); linter doesn't like this
      katexElement.textContent = decodedText;
    }
    script.parentNode.replaceChild(katexElement, script);
  });
});

onMounted(() => {
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
});
