const path = require("path");
const fs = require("fs");
const spawn = require("cross-spawn");
const pidInfoPath = path.resolve(__dirname, "./pid.json");

function readPid() {
  const pidStr = fs.readFileSync(pidInfoPath, { encoding: "utf-8" });
  return JSON.parse(pidStr).pid;
}

function writePid(pid) {
  fs.writeFileSync(pidInfoPath, JSON.stringify({ pid }), { encoding: "utf-8" });
}

const params = process.argv[2];
switch (params) {
  case "start":
    const pid = readPid();
    if (pid !== null) {
      console.log(`ksg-map-ultra is running, pid:${pid}`);
      break;
    }
    const viteProcess = spawn("npm", ["run", "dev"], {
      cwd: "../",
      stdio: "inherit",
    });
    console.log(`ksg-map-ultra start running, pid:${viteProcess.pid}`);
    writePid(viteProcess.pid);
    break;
  case "close":
    const pid2 = readPid();
    if (pid2 === null) {
      console.log("ksg-map-ultra is not running");
      break;
    }
    process.kill(11692, "SIGTERM");
    writePid(null);
    console.log(`ksg-map-ultra is closed, pid:${pid2}`);
    break;
}
