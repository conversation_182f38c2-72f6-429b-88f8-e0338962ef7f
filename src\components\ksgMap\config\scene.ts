import {
  Scene,
  Group,
  Color,
  TextureLoader,
  EquirectangularReflectionMapping,
} from "three";
import ctx from "../ctx";
import type { Config, SceneConfig } from "../types";
import bgImg from "../assets/images/bc4.png";

/**
 * 3D场景配置函数 - 创建和配置Three.js场景
 *
 * Scene是Three.js中的场景对象，它是一个容器，用来保存所有的3D对象、光源、相机等
 * 场景定义了3D世界的环境，包括背景、环境光照、雾效等全局设置
 *
 * 主要功能：
 * - 创建3D场景容器
 * - 设置场景背景和环境贴图
 * - 配置环境光照效果
 * - 创建知识图谱的根容器组
 *
 * @param config 场景配置参数对象
 * @returns 返回包含场景实例的对象
 */
export default function userScene(config: SceneConfig) {
  // 创建纹理加载器 - 用于加载图片资源作为纹理
  const texLoader = new TextureLoader();

  // 加载背景图片纹理
  // bc4.png 是一个全景背景图片，用于创建沉浸式的3D环境
  const texture = texLoader.load(bgImg);

  // 设置纹理映射方式为等距圆柱投影反射映射
  // EquirectangularReflectionMapping 适用于360度全景图片
  // 这种映射方式可以将平面的全景图正确地映射到3D球面上
  texture.mapping = EquirectangularReflectionMapping;

  // 创建3D场景实例
  const scene = new Scene();

  // 设置场景的环境贴图 - 用于环境反射和光照计算
  // 环境贴图影响场景中物体的反射效果和整体光照氛围
  scene.environment = texture;

  // 设置场景背景 - 用户看到的场景背景
  // 使用同一个纹理作为背景，创建一致的视觉环境
  scene.background = texture;

  // 设置背景强度 - 控制背景的亮度
  // 较低的值(0.02)创建柔和的背景效果，不会过于抢夺主体内容的注意力
  scene.backgroundIntensity = config.backgroundIntensity;

  // 设置背景模糊度 - 控制背景的清晰度
  // 0.0表示完全清晰，数值越大背景越模糊
  // 适度的模糊可以突出前景内容，创建景深效果
  scene.backgroundBlurriness = config.backgroundBlurriness;

  // 创建知识图谱容器组
  // Group是Three.js中的容器对象，用于组织和管理多个3D对象
  // 所有的知识节点、连线等元素都会添加到这个组中
  const group = new Group();

  // 设置容器组的位置
  // groupPosition 定义了整个知识图谱在3D空间中的位置
  // 使用展开运算符将数组 [x, y, z] 传递给 position.set()
  group.position.set(...config.groupPosition);

  // 将容器组添加到场景中
  // 这样容器组及其所有子对象都会被渲染
  scene.add(group);

  // 将场景和容器组保存到全局上下文中
  // 这样其他模块可以访问和操作场景内容
  ctx.scene = scene;      // 场景实例
  ctx.viewGroup = group;  // 知识图谱容器组

  // 返回场景实例
  return { scene };
}
