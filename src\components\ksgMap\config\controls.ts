import { KsgControls } from "../core/KsgControls";
import type {ControlsConfig } from "../types/index"
import ctx from "../ctx";

/**
 * 相机控制器配置函数 - 创建和配置自定义的相机控制器
 *
 * KsgControls是基于Three.js OrbitControls扩展的自定义控制器
 * 专门为知识图谱场景设计，提供了更精确的相机控制和交互体验
 *
 * 主要功能：
 * - 鼠标/触摸控制相机的旋转、缩放、平移
 * - 限制相机的移动范围和角度
 * - 支持阻尼效果，提供平滑的交互体验
 * - 针对知识图谱场景优化的控制逻辑
 *
 * @param option 控制器配置参数对象
 * @returns 返回包含控制器实例的对象
 */
export default function useControls(option: ControlsConfig) {
  // 创建自定义控制器实例
  const controls = new KsgControls(
    ctx.camera!,    // 要控制的相机对象
    ctx.viewGroup!, // 知识图谱的根容器组，用于计算控制范围

    // 事件监听的DOM元素 - 使用CSS2D渲染器的DOM元素
    // 重要：必须使用css2dRenderer的domElement而不是WebGL渲染器的
    // 因为CSS2D渲染器的元素在HTML层级中位置更高，能正确接收鼠标事件
    ctx.css2dRenderer?.domElement!
  );

  // 设置相机的初始位置
  // position数组格式：[x, y, z]
  controls.object.position.set(...option.position);

  // 设置相机的朝向目标点
  // target定义了相机看向的3D空间中的点
  // 相机会始终朝向这个目标点
  controls.target.set(...option.target);

  // 配置极角范围限制 - 控制垂直旋转的角度范围
  // 极角是从Y轴正方向到相机位置向量的夹角

  // 最小极角 - 相机能够旋转到的最高位置
  // 0表示正上方，Math.PI/2表示水平方向
  controls.minPolarAngle = option.minPolarAngle;

  // 最大极角 - 相机能够旋转到的最低位置
  // Math.PI表示正下方
  controls.maxPolarAngle = option.maxPolarAngle;

  // 配置相机距离限制 - 控制缩放的范围

  // 最小距离 - 相机能够靠近目标的最近距离
  // 防止相机过度靠近导致视觉问题
  controls.minDistance = option.minDistance;

  // 最大距离 - 相机能够远离目标的最远距离
  // 防止相机过度远离导致内容看不清
  controls.maxDistance = option.maxDistance;

  // 配置鼠标按键映射
  // 定义不同鼠标按键对应的操作（旋转、缩放、平移）
  controls.mouseButtons = option.mouseButtons;

  // 启用阻尼效果 - 提供平滑的交互体验
  // 阻尼使相机移动具有惯性，停止操作后会逐渐减速到静止
  controls.enableDamping = option.enableDamping;

  // 配置Y轴范围限制 - 专门为知识图谱场景设计的约束

  // Y轴最小范围 - 相机Y坐标的下限
  controls.yMinRange = option.yMinRange;

  // Y轴最大范围 - 相机Y坐标的上限
  controls.yMaxRange = option.yMaxRange;

  // Y轴增量 - 用于计算目标点的Y轴范围
  // 目标点Y轴范围为 [yMinRange, yMaxRange - yDelta]
  controls.yDelta = option.yDelta;

  // 将控制器实例保存到全局上下文中
  // 这样其他模块可以访问和操作相机控制器
  ctx.controls = controls;

  // 返回控制器实例
  return { controls };
}
