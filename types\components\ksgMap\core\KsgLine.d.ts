import { LineSegments } from "three";
import { Point } from "../types";
export default class KsgLine2 extends LineSegments {
    enableAnimation: boolean;
    /**流光速度 */
    speed: number;
    posit: number[];
    colors: number[];
    indexs: number[];
    segmentProgress: number[];
    random: number[];
    constructor(focusPoint: Point, focusChildren: Point[], opacity?: number);
    /**
     * 释放内存
     */
    dispose(): void;
    /**
     * 末位置更新函数
     */
    updateEndPosition(index: number, position: [number, number, number]): void;
    updateFocusPointPosition(position: [number, number, number]): void;
    /**
     * 更新透明度
     */
    updateOpacity(opacity: number): void;
    /**
     * 线条流光动画
     */
    update(): void;
    /**
     * 聚焦节点添加连线
     * @param newFocusChildren 增加聚焦节点的直接前驱节点
     */
    addFocusChildren(newFocusChildren: Point[]): void;
}
