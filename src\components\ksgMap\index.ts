import { LOAD_STATUS, MODE } from "./enums/index";
import type { Options } from "./config";
import { App, Component } from "vue";
import ksgMap from "./KsgMap.vue";
import dataLoader from "./hooks/dataLoader";
import "./assets/style/css2dLabel.css";
const KsgMap = ksgMap as Component<typeof ksgMap>;
// 按需导入
export { KsgMap, dataLoader };
export { LOAD_STATUS, MODE, Options };
// 全局注册
const KsgMapGlobal = {
  install(app: App) {
    app.component("KsgMap", KsgMap);
  },
};
export default KsgMapGlobal;
