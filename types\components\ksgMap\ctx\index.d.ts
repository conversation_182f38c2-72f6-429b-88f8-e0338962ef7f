import { Scene, PerspectiveCamera, Group, WebGLRenderer } from "three";
import { CSS2DRenderer } from "three/examples/jsm/renderers/CSS2DRenderer";
import { KsgControls } from "../core/KsgControls";
import { VIEW_MODE, MODE, LOAD_STATUS } from "../enums";
import KsgPoint from "../core/KsgPoints";
import KsgLine from "../core/KsgLine";
import KsgGraph from "../core/KsgGraph";
declare const ctx: Partial<Context>;
export default ctx;
/**
 * 全局上下文
 */
export type Context = {
    /**模式 */
    model: MODE;
    scene: Scene;
    viewGroup: Group;
    camera: PerspectiveCamera;
    renderer: WebGLRenderer;
    css2dRenderer: CSS2DRenderer;
    composer: any;
    controls: KsgControls;
    /**是不是在控制单中 */
    isControls: boolean;
    /**视角模式 */
    viewMode: VIEW_MODE;
    graph: KsgGraph;
    focusPointInfo: {
        pointId: string;
    };
    pointsLevelPager: {
        current: number;
        levelSize: number;
        total: number;
    };
    edge: any;
    levelSpace: number;
    maxLevelY: number;
    loadStatus: LOAD_STATUS;
    point: {
        radius: number;
        space: number;
    };
    hoverLabel: {
        offsetX: number;
        offsetY: number;
    };
    maxDistance: number;
    pointSpace: number;
    /**子图历史栈 */
    focusStack: string[];
    /**最大历史记录数 */
    focusStackMaxSize: number;
    focusBackToRoot: () => void;
    focusBack: () => void;
    /**视口范围 */
    viewRange: {
        minX: number;
        maxX: number;
        minY: number;
        maxY: number;
    };
    pointsMesh: KsgPoint;
    focusLine: KsgLine;
    focusChildrenIndex: Set<number>;
};
