import { Mesh } from "three";
/**
 * 聚焦进入动画
 * @param {Mesh} haloMesh 聚焦点光圈
 * @param {number} duration 持续时间
 */
export declare function pointHaloEnterAnimation(haloMesh: Mesh, duration?: number): Promise<unknown>;
/**
 * 消失动画
 * @param {Mesh} haloMesh 聚焦点光圈
 * @param {number} duration 持续时间
 */
export declare function pointHaloLeaveAnimation(haloMesh: Mesh, duration?: number): Promise<unknown>;
/**
 * 透明度变化动画
 * @param {number} from 起始透明度
 * @param {number} to 最终透明度
 * @param {number} duration 持续时间
 */
export declare function pointOpacityChangeAnimation(from: number, to: number, callback: (opacity: number) => void, duration?: number): Promise<unknown>;
