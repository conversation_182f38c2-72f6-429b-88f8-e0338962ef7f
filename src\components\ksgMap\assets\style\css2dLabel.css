.css2d-label,
.css2d-label-computed {
  --ml: 0px;
  --mr: 0px;
  --mt: 0px;
  --mb: 0px;
  --animation: label-enter;
  /* color: #fff;
  background: rgba(211, 235, 255, 0.11); */
  padding: 5px;
  border-radius: 3px;
  position: relative;
  /* max-width: 300px;
  max-height: 150px;
  overflow-x: hidden;
  overflow-y: auto; */
  cursor: pointer;
  font-size: 12px;
  font-family: alibaba-PuHuiTi;
  opacity: 0;
  animation: var(--animation) 0.15s ease-in forwards;
  pointer-events: none;
}
.css2d-label {
  z-index: 100000 !important;
}
.css2d-label-inner {
  margin: var(--mt) var(--mr) var(--mb) var(--ml);
  color: #fff;
  background: rgba(211, 235, 255, 0.11);
  padding: 5px;
  border-radius: 3px;
  pointer-events: all;
  max-width: 300px;
  max-height: 150px;
  overflow-x: hidden;
  overflow-y: auto;
}
.css2d-label-computed {
  width: fit-content;
  height: fit-content;
  position: absolute;
  top: 0px;
  animation: none;
}

@keyframes label-enter {
  from {
    opacity: 0;
    top: -10px;
  }
  to {
    opacity: 1;
    top: 0px;
  }
}

@keyframes label-leave {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.css2d-label-inner::-webkit-scrollbar {
  background: transparent;
  width: 8px;
  padding: 0px 2px;
}
.css2d-label-inner::-webkit-scrollbar-button {
  display: none;
}
.css2d-label-inner::-webkit-scrollbar-thumb {
  background: #6666;
  border-radius: 20px;
}
.css2d-label-inner::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.493);
}

/*
标签位置
*/
.top-right {
  top: -15px;
  right: 15px;
}

.top-left {
  /* left: -300px; */
  left: var(--width);
}

.bottom-right {
  top: var(--height);
  right: 15px;
}

.bottom-left {
  top: var(--height);
  left: var(--width);
}
